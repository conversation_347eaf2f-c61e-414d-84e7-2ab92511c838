% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/MakeSimBzoneEstDataset.R
\name{createSimLandUseDataset}
\alias{createSimLandUseDataset}
\title{Create the dataset to use for estimating land use simulation models.}
\usage{
createSimLandUseDataset()
}
\value{
A data frame containing the following components:
GEOID10 - Census block group 12-digit FIPS code
SFIPS - State FIPS code
TOTPOP10 - Population, 2010
HH - Households (occupied housing units), 2010
EMPTOT - Total employment, 2010
E5_RET10 - Retail jobs within a 5-tier employment classification scheme
E5_SVC10 - Service jobs within a 5-tier employment classification scheme
AC_TOT - Total geometric area of the census block group
AC_LAND - Total land area in acres
AC_UNPR - Total land area in acres that is not protected from development
D1D - Ratio of gross activity (employment + HUs) to land area
D2A_JPHH - Jobs per household calculated from EMPTOT and HH
D3bpo4 - Intersection density in terms of pedestrian-oriented intersections
having four or more legs per square mile
D4c - Aggregate frequency of transit service within 0.25 miles of block group
boundary per hour during evening peak period
TOTPOP10_5 - Population within 5 miles of block group centroid
EMPTOT_2 - Total employment within 2 miles of block group centroid
UA_NAME - Census urbanized area name
D5 - Destination accessibility measure that is the harmonic mean of
TOTPOP10_5 and EMPTOT_2
STATE - Postal code for state
UZA_NAME - Urbanized area name separated by state
LAT - Latitude of block group centroid
LNG - Longitude of block group centroid
TOTACT - Total number of jobs and households
D1D_SLD - Ratio of gross activity (employment + HUs) to unprotected land area
D2A_JPHH_SLD - Jobs per household included in the SLD
D2A_EPHHM - Employment and household entropy
PropSF - Proportion of households living in single-family dwellings
PropMF - Proportion of households living in multifamily dwellings
MedianInc - Median income of households
TransitVehMi - Annual fixed-route transit vehicle miles in urbanized area
TransitRevMi - Annual fixed-route transit vehicle revenue miles in urbanized
area
LocType - Location type (Urban, Town, Rural)
}
\description{
\code{createSimLandUseDataset} reads in the Census, Smart Location Database,
and Transit datasets and creates the dataset that is used for estimating
land use simulation models.
}
\details{
This function reads in the Census, Smart Location Database, and Transit
datasets and creates the dataset that is used for estimating land use
simulation models.
}
