% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/MakeSimBzoneEstDataset.R
\name{processTransitData}
\alias{processTransitData}
\title{Process transit data.}
\usage{
processTransitData()
}
\value{
A data frame containing the following components:
UaName - Urbanized area names corresponding to names in the Smart Location
Database
VehicleMiles - Annual vehicle miles of fixed-route transit service in the
urbanized area
RevenueMiles - Annual vehicle revenue miles of fixed-route transit service in
the urbanized area
}
\description{
\code{processTransitData} reads in 2010 public transit datasets from the
National Transit Database and calculates vehicle miles and vehicle revenue
miles for fixed-route transit services by urbanized area.
}
\details{
This function reads transit service, agency, and urbanized area files from
the National Transit Database for 2010 and creates a dataset of vehicle miles
and vehicle revenue miles for fixed route transit by urbanized area. The
urbanized area names are checked against the names in the Smart Location
Database and are made consistent.
}
