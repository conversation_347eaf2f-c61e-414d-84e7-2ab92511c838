% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/MakeSimBzoneEstDataset.R
\name{processSLD}
\alias{processSLD}
\title{Read in and process Smart Location Database (SLD)}
\usage{
processSLD()
}
\value{
A data frame containing the following components:
GEOID10 - Census block group 12-digit FIPS code in 2010
SFIPS - State FIPS code
TOTPOP10 - Population, 2010
HH - Households (occupied housing units), 2010
EMPTOT - Total employment, 2010
E5_RET10 - Retail jobs within a 5-tier employment classification scheme
E5_SVC10 - Service jobs within a 5-tier employment classification scheme
AC_TOT - Total geometric area of the block group
AC_LAND - Total land area in acres
AC_UNPR - Total land area in acres that is not protected from development
D1D - Gross activity density (employment + HUs) on unprotected land
D2A_JPHH - Jobs per household
D3bpo4 - Intersection density in terms of pedestrian-oriented intersections
having four or more legs per square mile
D4c - Aggregate frequency of transit service within 0.25 miles of block group
boundary per hour during evening peak period
TOTPOP10_5 - Population within 5 miles of block group centroid
EMPTOT_2 - Jobs within 2 miles of block group centroid
UA_NAME - Urbanized area name
D5 - Destination accessibility measure that is the harmonic mean of
TOTPOP10_5 and EMPTOT_2
}
\description{
\code{processSLD} reads in an augmented Smart Location Database in the form
of a 'tibble' and processes it.
}
\details{
This function reads in an augmented version of the Smart Location Database
used in the estimation of the multi-modal travel model by Liming Wang et.al.
The number of columns are reduced to those that will be used in the
estimation of land use simulation modules. In addition, the records are
limited to records for census block groups located in states and the District
of Columbia. A destination accessibility measure is calculated as well.
}
