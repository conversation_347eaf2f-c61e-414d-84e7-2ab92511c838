% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/MakeSimBzoneEstDataset.R
\name{processCensusHousingInc}
\alias{processCensusHousingInc}
\title{Process census block group housing and median income data downloaded using
the Census API.}
\usage{
processCensusHousingInc(Data_df, BldSzVars_, IncVar)
}
\arguments{
\item{Data_df}{A data frame produced the get_acs function in the tidycensus
package.}

\item{BldSzVars_}{A string vector identifying the names of the building
size variables in the ACS dataset.}

\item{IncVar}{A string identifying the name of the median income variable in
the ACS dataset.}
}
\value{
A data frame containing the following components:
GeoID - Census block group ID
Total - Total number of dwelling units
NumSF - Number of single-family dwelling units
NumMF - Number of multifamily dwelling units
PropSF - Proportion of dwelling units that are single family
PropMF - Proportion of dwelling units that are multifamily
MedianInc - Median household income
}
\description{
\code{processCensusHousingInc} processes Census block group housing and
median income data for states and the District of Columbia in the United
States downloaded using the Census API.
}
\details{
This function processes block group level data for numbers of dwelling units
by type and household median income that has been downloaded using the Census
API. Housing units groups are aggregated into to categories, single-family
and multifamily. Single family units include all detached dwelling units.
Multifamily include all other dwelling units. Note to use this function, you
must have a Census API key. You need to get a Census API key from this
website: https://api.census.gov/data/key_signup.html Then uncomment the
following line of code and insert the key where indicated
census_api_key(INSERT CENSUS API KEY HERE, install = TRUE)
}
