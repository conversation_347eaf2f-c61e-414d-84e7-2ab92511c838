# Author: <PERSON>

# VEModel Package Code

#####################
# RUNTIME ENVIRONMENT
#####################

#ACCESS R ENVIRONMENT FOR MODEL RUN
#==================================
#' Access an R environment for the runtime installation.
#'
#' \code{modelEnvironment} returns an environment for managing the ve.runtime directory and its
#' components, the default runtime parameters and other runtime needs. That environment contains the
#' default RunParam_ls structure plus other components needed to manage global state for VEModel
#' classes.
#'
#' \code{modelEnvironment} holds the RunParam_ls settings from the runtime directory (or its
#' "config" subdirectory if that exists). The runtime directory is either the current directory or
#' specified in a system environment variable VE_RUNTIME; see \code{setRuntimeDirectory()}.
#' 
#' The runtime environment settings form the basis for settings used by individual models, and can
#' define things like the RandomSeed or DatastoreType that are rarely changed in individual models.
#'
#' @section Model Environment:
#'
#' VEModel supports the following directory structures for models to configure the location of
#' $export/$extract and $query artifacts as well as the models themselves. These are in addition
#' to the configuration parameters for the framework (see
#' \code{visioneval::defaultVERunParameters}).
#'
#' The visioneval framework itself defaults to a simpler directory structure (for "classic"
#' VisionEval models) which apply only to the inputs and results for a single model. If you "source"
#' a run_model.R script without loading the VEModel package, you will get the classic output
#' structure (which can still be opened by VEModel). If you load the VEModel package, it will
#' understand the classic structure, but will use these settings to manage new models, new model
#' runs, and outputs generated using VEModel, VEResults or VEQuery functions. Override them in a
#' model-specific VisionEval.cnf file (in ModelDir) to make it as much like the old behavior as you
#' wish. Set the various directories to "." or "" to ignore them and just to use "ModelDir", which
#' is classically the location of the \code{run_model.R} script.
#'
#' Here is a summary of the VEModel defaults. To see parameters and defaults defined in the
#' visioneval framework itself, see \code{visioneval::defaultVERuntimeParameters}.
#'
#' When VEModel loads a model, it sets ModelDir to the full path of the model (which may or may not
#' include ModelRoot, e.g. if a model is opened with an absolute path). The visioneval framework
#' itself uses the working directory for ModelDir (where "run_model.R" will presumably be found).
#'
#' Then VEModel will open the model configuration file by looking in ModelDir for a visioneval.yml
#' file (see \code{loadModelConfiguration}).
#'
#' If that configuration does not override the standard locations, and no ModelSetup is defined (in
#' ModelDir/ScriptsDir or in the root ModelDir), the ModelScriptFile (see visioneval parameters)
#' will be sought using the ModelScript pattern, in ModelDir/ScriptsDir and then in ModelDir (see
#' the framework for ModelDir definition).
#'
#' QueryDir is the default location for Query Specification definition files. OutputDir receives
#' results of the $extract/$export and $query functions. DisplayUnitsFile is the basename of the
#' file used to provide model units. It will be sought in ParamDir for the model, and if not found
#' there will be sought in ve.runtime/config. QueryFileName is a default for creating a query if no
#' other name is provided. It will be disambiguated by appending digits to the root name (i.e.
#' before the .VEqry extenstion). QueryOutputTemplate describes the filename generated by $query
#' within its results directory (see the VEQuery R6 object documentation).
#'
#' \describe{
#'   \item{ModelDir}{Default value is provided in the visioneval framework. In VEModel, ModelDir is
#'   constructed when a model is loaded. It is then used as the root for most of the following parameters,
#'   as well as the model parameters such as ParamDir and InputsDir defined in the framework.}
#'
#'   \item{ModelRoot}{Where to search in ve.runtime for model directories (default "models")}
#'   \item{ModelSetup}{File name for 'new style' model (default "model_setup.yml")}
#'   \item{ModelScript}{Regular expression for run_model file name (default "run_model.R")}
#'   \item{ScriptsDir}{Directory within a model to search for run_model.R or RunStep scripts
#'   (default "scripts")}
#'   \item{ResultsDir}{Directory within a model to place the current results, and to provide a
#'   basename for archived results (default "results")}
#'   \item{QueryDir}{Where to look for query definitions for this model (default "queries")}
#'   \item{OutputDir}{Where to put results generated by $extract/$export or $query, relative to
#'   ModelDir (default "output")}
#'   \item{DisplayUnitsFile}{Name of the file describing unit conversions to be applied to
#'   Datastore fields when using $export/$extract (default "display_units.csv")}
#'   \item{QueryFileName}{Default name for a newly constructed query (within QueryDir) (default
#'   "New-Query.VEqry")}
#'   \item{QueryOutputTemplate}{File template for output from the $query function, within
#'   OutputDir/query_\%datetime\% (default "Measures_\%scenario\%_\%years\%_\%geography\%.csv")}
#' }
#'
#' @param ve.new.env an environment to use in place of package built-in (default use VEModel:::ve.env)
#' @return an R environment "ve.env"
#' @import visioneval
#' @export
runtimeEnvironment <- function(ve.new.env=NULL) {
  # Operates on "ve.env" attached to search path (and manipulated also by VEStart and VEBuild)
  # The ve.env environment is accessed via VEModel::runtimeEnvironment.

  # Link VEModel environment to attached "ve.env" created in VE-Bootstrap.R or VEStart
  ve.env <- if ( ! "ve.env" %in% search() ) {
    attach(NULL,name="ve.env")
  } else {
    as.environment("ve.env")
  }

  # Load ve.env from ve.new.env
  # NOTE: probably obsolete since everyone should be using the attached "ve.env" environment
  if ( ! missing(ve.new.env) && is.environment(ve.new.env) ) {
    for (n in ls(ve.new.env, all.names=TRUE)) assign(n, get(n, ve.new.env),ve.env)
  }
  # Back stop to make sure we have a place to load system and model configurations
  if ( ! "RunParam_ls" %in% ls(ve.env) ) assign("RunParam_ls",list(),envir=ve.env)
  ve.env
}

# Initialize VEModel
#' Set up initial VEModel environment
#'
#' @return None
#' @export
initVisionEval <- function() {
  message("Loading VisionEval 4.0!")
  ve.env <- runtimeEnvironment()      # establish VEModel environment (possibly set up externally by VEStart)
  getSetup(reload=TRUE)               # reload global RunParam_ls; also will align with ve.env$ve.runtime
  ModelRoot <- getModelDirectory()    # Full path built from ve.runtime and global visioneval.cnf model directory name
  if ( ! dir.exists(ModelRoot) ) {
    message("Creating runtime '",basename(ModelRoot),"' directory")
    dir.create(ModelRoot,recursive=TRUE,showWarnings=FALSE)
  }
  message("Running in ",ve.env$ve.runtime)
  setwd(ve.env$ve.runtime)
  NULL
}

# Package defaults for VisionEval getRunParameter
# Some of these are different from the framework defaults (e.g. ResultsDir).
# The package defaults will override.

default.parameters.table = list(
  ModelRoot           = "models",         # Used to search in VERuntime for model setups
  ScriptsDir          = "scripts",        # Will collapse to '.' if directory does not exist
  ResultsDir          = "results",        # Used by the model to hold results for each model stage
  OutputDir           = "output",         # Used by various exporters to hold file-based results
  QueryDir            = "queries",        # Home for defined queries within the ModelDir
  ScenarioDir         = "scenarios",      # Root for defined scenarios
  ScenarioConfig      = "scenarios.cnf",  # Configuration file for scenarios
  ExtractRootName     = "Extract",        # used by VEResultsList$export to make "output/Extract_<timestamp>" folder
  MetadataName        = "Metadata",       # used by VEResultsList$export to name metadata table within output
  DisplayUnitsFile    = "display_units.csv",
  QueryFileName       = "New-Query",              # For making a VEQuery file
  QueryOutputTemplate = "Query_%queryname%.Rda",  # For the generated outputs (in stage results folder next to Datastore)
  QueryExtractTable   = "QueryExtract",   # For VEQuery$export to create table in output folder
  RunStatusDelay      = 60,               # seconds between status updates when multi-processing
  RunPollDelay        = 2,                # seconds between status poll for multi-process completion
  Exporters           = list(Default="csv") # Set Default Exporter format (configurable in global or model visioneval.cnf)
)

#GET DEFAULT PARAMETERS
#======================
#' Hook to add default values for key parameters in the VEModel package
#'
#' \code{VEPackageRunParameters} extends \code{visioneval::defaultVERuntimeParameters} to provide
#' additional Parameters and Defaults for VEModel functions that can be accessed seamlessly. You can
#' call this function directly (e.g. to see what parameters are defined and defaulted in VEModel).
#' Internally VEModel uses \code{visioneval::defaultVERuntimeParameters} to access these parameters,
#' so it doesn't have to remember whether specific defaults are defined in visioneval itself or in
#' VEModel, or some other package (and one can transparently move the default definitions back and forth).
#'
#' @return a named list of parameters with default values defined in this package
#' @import visioneval
#' @export
VEPackageRunParameters <- function() {
  # Add the source attribute to the default.parameters.table and return the resulting list
  visioneval::addParameterSource(default.parameters.table,"Package VEModel Default")
}

#LOAD RUNTIME CONFIGURATION
#==========================
#' Load a VisionEval.cnf file from the runtime directory into the runtime environment
#'
#' \code{loadRuntimeConfig} merges a configuration file from a directory into the runtime R
#' environment. With no parameters it will read and merge the file's contents into the system
#' runtime paramters list (so call it after changing the configuration file to bring the in-memory
#' version up to date). See \code{visioneval::loadConfiguration} for more details.
#'
#' @return Parameters from the runtime configuration file
#' @import visioneval
#' @export
loadRuntimeConfig <- function() {
  # Function loads configuration from ParamDir/VisionEval.cnf
  # ParamDir defaults to ve.runtime
  ve.env <- runtimeEnvironment()
  if ( is.null(ve.env$ve.runtime) ) setRuntimeDirectory() # VE_RUNTIME or getwd()
  return( visioneval::loadConfiguration(ParamDir=ve.env$ve.runtime) )
}

#GET RUNTIME SETUP
#=================

#' Return runtime base RunParam_ls (loading it if not present)
#'
#' \code{getSetup} gets a subset of the current runParameters by name. It does NOT supply default
#' values. It returns only the ones that are defined. As a side effect, will create the package
#' ve.env via loadRuntimeConfig.
#'
#' @param paramNames is a character vector of parameter names identifying a subset of runParameters
#'   to retrieve. If not provided, return all defined parameters (but not any that are defaulted).
#' @param object identifies which parameter set to get. NULL (default) returns runtime parameters.
#'   Otherwise object should be a VEModel, a VEModelStage, or a VEResultsList object
#' @param fromFile a logical value; if TRUE, return base configuration file (loadedParam_ls),
#'   otherwise parameters as configured into model's RunParam_ls.
#' @param reload a logical; if TRUE and retrieving ve.runtime configuration, re-read configuration
#'   file
#' @return A list of defined run parameters (possibly empty, if no parameters are defined)
#' @export
getSetup <- function(object=NULL,paramNames=NULL,fromFile=TRUE,reload=FALSE) {
  ve.env <- runtimeEnvironment()
  if ( is.list(object) ) { # assume its a Param_ls list
    RunParam_ls <- object
  } else if ( inherits(object,"VEResults") ) {
    RunParam_ls <- VEResults$RunParam_ls
  } else if ( is.null(object) ) {
    if ( reload ) ve.env$RunParam_ls <- ve.env$loadedParam_ls <- loadRuntimeConfig()
    object <- ve.env
  } # else presume ist a VEModel or VEModelStage or something else with RunParam_Ls/loadedParam_ls
  RunParam_ls <- if ( ! fromFile ) object$RunParam_ls else object$loadedParam_ls
  if ( is.character(paramNames) ) {
    RunParam_ls <- RunParam_ls[names(RunParam_ls) %in% paramNames]
  }
  return(invisible(RunParam_ls))
}

#VIEW RUNTIME SETUP
#==================
#' View elements of a RunParam_ls structure
#'
#' \code{viewSetup} will retrieve and display the contents of a RunParam_ls structure. Can view
#' values set for the VisionEval runtime, values set for a model, or values set for a singele model
#' stage. To view the system default parameters, just run the framework function
#' \code{visioneval::defaultVERunParameters()}.
#'
#' @param object identifies the parameter set to write. NULL (default) reports what is in
#' ve.runtime, Otherwise the object should be a VEModel, VEModelStage, or VEModelScenarios.
#' @param Param_ls If provided (and object is NOT provided), view this list instead of looking up
#'   via getSetup.
#' @param viewSource If TRUE inject the parameter source into the display as "pseudo YAML",
#'   otherwise just name and value for each parameter (as YAML).
#' @param shorten If TRUE pass over returned parameters and trim off the RuntimeDirectory path
#' @param fromFile if TRUE, shows the parameters "loaded" from a visioneval.cnf file (i.e. the set
#'   that the RunParam_ls gets built from during model$configure). If FALSE, you can view the
#'   RunParam_ls itself, which is what the model would actually run with. Comparing these
#'   (ViewSetup(...,fromFile=TRUE) and ViewSetup(...,fromFile=FALSE) will show what got built or changed
#'   during the configure process.
#' @param showParsedScript show the ParsedScript element in RunParam_ls (default FALSE; it's
#'   usually not interesting)
#' @param ... Additional arguments to \code{cat} used internally
#' @importFrom yaml as.yaml
#' @export
viewSetup <- function(object=NULL,Param_ls=NULL,fromFile=FALSE,
  shorten=TRUE,viewSource=FALSE,showParsedScript=FALSE,...) {
  if ( ! is.null(object) || is.null(Param_ls) ) {
    Param_ls <- getSetup(object=object,fromFile=fromFile)
  } # else view Param_ls from function parameters
  if ( ! showParsedScript ) Param_ls <- Param_ls[ ! names(Param_ls) %in% "ParsedScript" ]
  paramNames <- names(Param_ls)
  for ( i in seq(Param_ls) ) { # format manually
    nm <- paramNames[i]
    param <- Param_ls[[i]]
    cat(nm,":",sep="")
    if ( is.list(param) ) {
      if ( viewSource ) {
        src <- attr(param,"source")
        if ( shorten ) src <- sub( getRuntimeDirectory(),"", src ) # blunt brush to shorten directories
        if ( ! is.null(src) ) {
          cat("  #",src,"\n")
        }
      } else cat("\n")
      cat( "  ",sub("  $","",gsub("\\n","\n  ",yaml::as.yaml(param))),sep="") 
    } else {
      yamlp <- yaml::as.yaml(param)
      if ( shorten ) yamlp <- sub( getRuntimeDirectory(),"", yamlp )
      if ( viewSource ) {
        # change first newline plus name into a row with the src
        cat( sub("\\n",paste0(" # ",src,"\n"),yamlp),sep="")
      } else cat( yamlp )
    }
  }
  # cat(Paraml,...)
}

#UPDATE SETUP
#' Change setings in either a file-based or working parameter list
#'
#' This function will add or change parameters in a settings list. It will NOT re-write the settings
#' to a file. Use inFile=TRUE here, then use \code{writeSetup} to re-write the changed settings.
#' Otherwise the changes are just made in memory (and only changes for inFile=FALSE will do
#' anything).
#'
#' @param object identifies the parameter set to write. NULL (default) uses 
#' ve.runtime. Otherwise the object should be a VEModel or VEModelStage.
#' @param inFile a logical. If TRUE, alter the loadedParam_ls, otherwise alter RunParam_ls.
#'   Generally, leave inFile=TRUE. If you alter RunParam_ls you can re-run the model without
#'   running model$configure, but that's risky since parameters can depend on each other in
#'   sometimes unexpected ways
#' @param Source a character string describing the source assigned to these parameters
#' @param Param_ls a named list of parameters to add (or replace)
#' @param drop a character vector of parameters names that will be dropped rather than replaced
#' @param ... individual named parameters that will be added to (or replace) settings
#' @import visioneval
#' @export
updateSetup <- function(object=NULL,inFile=TRUE,Source="interactive",Param_ls=list(),drop=character(0),...) {
  # TODO: for purposes of model Run Status, change status of "object" to "now" so when we next run
  # the model, stages with a "RunComplete" time stamp earlier than now will all be marked for reset.
  # Probably not a big deal since manual reset is better in any case.

  # merge ... into Param_ls (expecting a named list of arbitrary parameters)
  Param_ls <- visioneval::mergeParameters(
    Param_ls,
    visioneval::addParameterSource(list(...),Source)
  )
  # locate the settings to work on, using object and inFile
  if ( is.null(object) ) object <- runtimeEnvironment()
  # udpate the settings using the framework merge settings function
  param.name <- if ( inFile ) "loadedParam_ls" else "RunParam_ls"

  # Names to drop from Param_ls
  if ( length(drop)>0 ) {
    for ( nm in drop ) object[[param.name]][[nm]] <- NULL
  }
  # Add in other parameters
  object[[param.name]] <- visioneval::mergeParameters(object[[param.name]],Param_ls)

  return( invisible(object[[param.name]]) )
}

#WRITE RUNTIME SETUP
#===================
#' Write a runtime setup file
#'
#' \code{writeSetup} writes a RunParam_ls list into the YAML configuration file associated
#'   with the VisionEval runtime, a model, or a specific model stage.
#'
#' @param object identifies the parameter set to write. NULL (default) writes to
#' ve.runtime/visioneval.cnf. Otherwise the object should be a VEModel or VEModelStage.
#' @param filename the name of the configuration file to write. If NULL (the default), write to the
#'   file associated with the existing parameter list of the object.
#' @param fromFile a logical value; if TRUE, return base configuration file (loadedParam_ls),
#'   otherwise parameters as configured into model's RunParam_ls.
#' @param overwrite if TRUE, overwrite any existing configuration file; otherwise abort with error
#' @return The filename that was written, or character(0) with a warning if the file could not
#'   be written
#' @importFrom yaml write_yaml
#' @export
writeSetup <- function(object=NULL,filename=NULL,fromFile=TRUE,overwrite=FALSE) {

  Param_ls <- getSetup(object=object,fromFile=fromFile)
  ve.env <- runtimeEnvironment()
  ParamDir <- ve.env$ve.runtime # Default to save parameters to root of runtime directory
  if ( is.null(object) ) {
    ParamName <- "runtime"
  } else {
    if ( inherits(object,"VEModel") ) {
      ParamName <- object$modelName
      ParamDir <- object$modelPath
    } else if ( inherits(object,"VEModelStage") ) {
      ParamName <- object$Name
      ParamDir <- object$Path
    } else if ( inherits(object,"VEModelScenarios") ) {
      ParamName <- object$scenarioDir
      ParamDir <- dirname(object$scenarioPath)
    } else if ( ! is.list(object) ) {
      stop (
        writeLog(paste("Warning: No directory to write visioneval.cnf for",ParamName),Level="error")
      )
    } else {
      ParamName <- "Parameter List"
      # if object was a list, fall through to find filename
    }
  } 

  if ( is.null(filename) ) {
    ParamPath <- attr(Param_ls,"FILE")
    if ( is.null(ParamPath) ) {
      ParamPath <- file.path(ParamDir,"dump-visioneval.cnf")
    }
  } else if ( ! isAbsolutePath(filename) ) {
    ParamPath <- file.path(ParamDir,filename)
    # attr(Param_ls,"FILE") <- ParamPath # This seems too aggressive and may confuse things later.
  } else { # absolute path provided as filename parameter
    ParamPath <- filename
  }

  writeLog(paste("Writing configuration for",ParamName,"to",ParamPath),Level="warn")

  if ( file.exists(ParamPath) && ! overwrite ) {
    writeLog(paste("Renaming existing configuration file:",backupName <- paste0(ParamPath,".bak")),Level="info")
    file.copy(ParamPath,paste0(ParamPath,".bak"))
  }

  yaml::write_yaml(Param_ls,file=ParamPath)
  
  return(invisible(Param_ls))
}

#' Set the VisionEval runtime directory for model management
#'
#' \code{setRuntimeDirectory} runs when the VEModel package is loaded. If R starts with a different
#' working directory, this function can replace \code{setwd()} as it can be used in a single call
#' to set the VEModel runtime directory and to change the working directory.
#'
#' The working directory at the time this function was called is also saved as ve.env$start.dir.
#' It is consequently feasible to move back and forth between alternative runtime directories.
#'
#' One can return to the original working directory using a line like this:
#' \code{setRuntimeDirectory(getRuntimeEnvironment()$start.dir)}
#'
#' When that line runs, start.dir will again be reset, so running that line repeatedly will toggle
#' between two directories.
#'
#' @param Directory a specific directory (absolute or relative to getwd()) to use as the runtime. If
#' Directory is not provided, looks for a system environment variable VE_RUNTIME, and if that is not
#' defined, then uses the working directory.
#' @return The normalized path to the directory that has been selected as the ve.runtime
#' @export
setRuntimeDirectory <- function(Directory=NULL) {
  ve.env <- runtimeEnvironment()
  if ( is.null(Directory) ) {
    Directory <- if ( ! exists("ve.runtime",envir=ve.env,inherits=FALSE) ) getwd() else ve.env$ve.runtime
  } else {
    Directory <- normalizePath(Directory,winslash="/",mustWork=FALSE)
    if ( ! dir.exists(Directory) ) {
      Directory <- getwd()
    }
  }
  # save the working directory from before this call
  ve.env$start.dir <- getwd()
  if ( getwd() != Directory ) {
    setwd(Directory)
  }
  return( ve.env$ve.runtime <- getwd() )
}

# GET MODEL DIRECTORY
# ===================
# Get the model directory (ve.runtime/models)
#' Get the model directory
#' Return the directory (usually "models" within \code{getRuntimeDirectory()}) from which models will
#'   be opened and in which models will be sought.
#' @return The full path of the models directory
#' @export
getModelDirectory <- function() {
  return ( file.path( getRuntimeDirectory(), visioneval::getRunParameter("ModelRoot") ) )
}

# GET RUNTIME DIRECTORY
# ========================
# Get the runtime directory (ve.runtime)
#' Get the runtime directory
#' Return the runtime directory established when VEModel package is loaded or by a later call to
#'   \code{setRuntimeDirectory}. If the runtime directory has not yet been set, set it to the
#'   working directory.
#' @return The ve.runtime directory from the attached runtime environment, "ve.env"
#' @export
getRuntimeDirectory <- function() {
  ve.env <- runtimeEnvironment()
  Directory <- ve.env$ve.runtime
  if ( is.null(Directory) ) Directory <- setRuntimeDirectory()
  return(Directory)
}

# MAKE A UNIQUE FILE NAME
#========================
#  Get unique file name based on newName in folder newPath
#  NewPath is the directory it should go in, newName is the name to disambiguate
getUniqueName <- function(newPath,newName) {
  newModelPath <- file.path(newPath,newName)
  tryName <- newName; try <- 1
  while ( dir.exists(newModelPath) ) {
    tryName <- paste0(newName,"(",try,")")
    newModelPath <- file.path(newPath,tryName)
    try <- try+1
  }
  return (newModelPath)
}

# CHECK IF PATH IS ABSOLUTE
# =========================
#  return TRUE if modelPath looks like an absolute file path
isAbsolutePath <- function(modelPath,collapse=TRUE) {
  absolute <- grepl("^([[:alpha:]]:|[\\/])",modelPath)
  if (collapse) absolute <- any(absolute)
  return(absolute)
}

# NORMALIZE A VISIONEVAL PATH
# ===========================
# Like the R built-in normalizePath, but better handling for missing path components
# NOTE: Do we need this? Standard normalize path seems to handle empty path elements...
# Always does winslash="/"
normalizePath <- function(
  paths,                # character vector of file paths
  RootDir="",           # prefixed onto all relative paths
  mustWork=FALSE,       # or NA (warning) or TRUE (error) if not existing
  winslash="/"          # passed through
) {
  return( base::normalizePath(paths,winslash=winslash,mustWork=mustWork) )
  # elements <- unlist( lapply(
  #   strsplit(paths,split="[\\/]"),
  #   function(x) {
  #     x[!nzchar(x)] <- "."
  #     paste(x,collapse="/")
  #   }
  # ) )
  # RootDir <- RootDir[1]
  # if ( nzchar(RootDir) ) {
  #   relative <- !isAbsolutePath(elements,collapse=FALSE)
  #   elements[relative] <- paste(RootDir,elements[relative],sep="/")
  # }
  # return( base::normalizePath(elements,winslash=winslash,mustWork=mustWork) )
}

# MOVE LOG FUNCTIONS INTO VEModel NAMESPACE
# =========================================
#' @import visioneval
writeLog <- visioneval::writeLog
writeLogMessage <- visioneval::writeLogMessage
initLog <- visioneval::initLog

# HELPER TO LIST UNIQUE SOURCES IN RUN PARAMETERS
# ===============================================
# List unique sources in a parameter list
uniqueSources <- function(Param_ls,shorten=NULL) {
  sources <- sapply(Param_ls,function(p) attr(p,"source"))
  if ( is.null(sources) ) {
    writeLog(c("'sources' attribute is null in uniqueSources",.traceback(1)),Level="info")
    sources <- "NULL"
  } else {
    sources <- unique(sources)
    if ( ! is.null(shorten) ) sources <- sub(paste0(shorten,"/"),"",sources)
  }
  return(sources)
}

# BUILD INSTALLABLE MODEL INDEX
# =============================
#' Get index of models and variants available for installModel()
#' @param reset if TRUE, rebuild the available model index
#' @return a list of available models and variants 
#' @export
getModelIndex <- function(reset=FALSE) {

  # Uses the attached "ve.env" to cache models and variants
  ve.env <- runtimeEnvironment()
  if ( ! reset && "modelIndex" %in% names(ve.env) ) return(ve.env$modelIndex)

  # Hack for developing packages with pkgload if package is not
  # already built; not working in 4.1.3 due to environment/search problem
  # for test functions - usePkgload appears only to work for VEModel
  # or visioneval (where outside environment doesn't matter)
  pkgs <- utils::installed.packages(fields="Package")
  VE.pkgs <- grep("^VE",pkgs[,"Package"],value=TRUE)
  loaded.packages <- grep("package:VE",search(),value=TRUE)
  for ( pkg.load in loaded.packages ) {
    pkg <- sub("package:","",pkg.load)
    if ( ! pkg %in% names(VE.pkgs) ) {
      VE.pkgs[pkg] <- pkg
    }
  }
  modelPaths <- sapply(VE.pkgs, function(p) system.file("models",package=p))
  modelPaths <- dir(modelPaths,pattern="model-index.cnf",recursive=TRUE,full.names=T)
  modelIndex <- list()
  for ( confPath in modelPaths ) {
    confPackage <- basename(sub("[/\\]models[/\\].*","",confPath))
    if ( confPackage == "inst" ) {
      confPackage <- paste("Virtual",basename(dirname(sub("[/\\]models[/\\].*","",confPath))))
    }
    index <- try( yaml::yaml.load_file(confPath) )
    if ( ! "variants" %in% names(index) ) {
      modelName <- names(index)
    } else {
      modelName <- basename(dirname(confPath)) # sub-directory in VEModel/models
      tmp <- list()
      tmp[modelName] <- list(variants=index)
      index <- tmp
    }
    # Get here with modelIndex a list of model names, whose elements are a list of model variants
    for ( m in names(index) ) {
      if ( ! "variants" %in% names(index[[m]]) ) {
        writeLog(paste("No model variants in",m,"\n"),Level="warn")
        next
      }
      vars <- index[[m]]$variants
      if ( length(vars)>0 ) {
        if ( any(dupes <- names(vars) %in% modelIndex[[m]]) ) {
          writeLog(paste0("While processing ",basename(dirname(confPath)),":"),Level="warn")
          writeLog(paste("Duplicated model variant",paste0(m,":",paste(names(vars)[dupes],collapse=","))),Level="warn")
        }
        modelIndex[[m]][ names(vars) ] <- vars
        for ( v in names(vars) ) {
          modelIndex[[m]][[v]]$ModelDir <- dirname(confPath)
          attr(modelIndex[[m]][[v]],"Package") <- confPackage
        }
      } else {
        writeLog(paste("No model variants in",modelName),Level="error")
        modelIndex[[m]] <- list()
        attr(modelIndex[[m]],"Package") <- confPackage
      }
    }
  }
  return( invisible(ve.env$modelIndex <- modelIndex) )
}

# DUMP MODEL INDEX, SHOWING SOURCES
#==================================
#' Report package source of model variants and private status
#' @param reset if TRUE, rebuild the index (e.g. after installing new VE packages)
#' @param private if TRUE, list private models as well as public ones
#' @return a data.frame listing all models, variants and their package source
#' @export
showModelIndex <- function(reset=FALSE, private=FALSE) {
  modelIndex <- getModelIndex(reset)
  modelSources <- list()
  for ( m in names(modelIndex) ) {
    writeLog(paste("Model",m,"Variants",paste(names(modelIndex[[m]]),collapse=", ")),Level="info")
    if ( length(modelIndex[[m]]) == 0 ) {
      modelSources$Model   <- c(modelSources$Model,m)
      modelSources$Variant <- c(modelSources$Variant,"None")
      modelSources$Package <- c(modelSources$Package,attr(modelIndex[[m]],"Package"))
      if ( private ) modelSources$Private <- "Unknown"
      writeLog("Added model with no variants",Level="info")
    } else {
      for ( v in names(modelIndex[[m]]) ) {
        is.private <- "private" %in% names(modelIndex[[m]][[v]]) && modelIndex[[m]][[v]]$private
        if ( private || ! is.private ) {
          modelSources$Model   <- c(modelSources$Model,m)
          modelSources$Variant <- c(modelSources$Variant,v)
          modelSources$Package <- c(modelSources$Package,attr(modelIndex[[m]][[v]],"Package"))
          if ( private ) {
            modelSources$Private <- c(
              modelSources$Private,
              if( is.private ) "Private" else "Public"
            )
          }
          writeLog(paste("Added model",m,"Variant",v),Level="info")
        }
      }
    }
  }
  return( as.data.frame(modelSources) )
}

