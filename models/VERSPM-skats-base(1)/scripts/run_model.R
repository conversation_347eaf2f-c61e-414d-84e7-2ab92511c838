requirePackage(VEPowertrainsAndFuelsAP2022)
requirePackage(VETravelDemandWFH)
requirePackage(VETravelPerformanceDL)

for (Year in getYears()) {
  runModule("CreateHouseholds", "VESimHouseholdsSKATS", RunFor = "AllYears", RunYear = Year)
  runModule("PredictWorkers", "VESimHouseholdsSKATS", RunFor = "AllYears", RunYear = Year)
  runModule("AssignLifeCycle", "VESimHouseholdsSKATS", RunFor = "AllYears", RunYear = Year)
  runModule("PredictIncome", "VESimHouseholdsSKATS", RunFor = "AllYears", RunYear = Year)
  runModule("PredictHousing", "VELandUseDLSKATS", RunFor = "AllYears", RunYear = Year)
  runModule("LocateEmployment", "VELandUse", RunFor = "AllYears", RunYear = Year)
  runModule("AssignLocTypes", "VELandUse", RunFor = "AllYears", RunYear = Year)
  runModule("Calculate4DMeasures", "VELandUse", RunFor = "AllYears", RunYear = Year)
  runModule("CalculateUrbanMixMeasure", "VELandUse", RunFor = "AllYears", RunYear = Year)
  runModule("AssignParkingRestrictions", "VELandUse", RunFor = "AllYears", RunYear = Year)
  runModule("AssignDemandManagement", "VELandUse", RunFor = "AllYears", RunYear = Year)
  runModule("AssignCarSvcAvailability", "VELandUseDLSKATS", RunFor = "AllYears", RunYear = Year)
  runModule("AssignTransitService", "VETransportSupply", RunFor = "AllYears", RunYear = Year)
  runModule("AssignRoadMiles", "VETransportSupply", RunFor = "AllYears", RunYear = Year)
}
