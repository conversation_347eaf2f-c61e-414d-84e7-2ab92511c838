{"R": {"Version": "4.4.3", "Repositories": [{"Name": "CRAN", "URL": "https://cloud.r-project.org"}]}, "Bioconductor": {"Version": "3.20"}, "Packages": {"BiocGenerics": {"Package": "BiocGenerics", "Version": "0.48.1", "Source": "Bioconductor", "Title": "S4 generic functions used in Bioconductor", "Description": "The package defines many S4 generic functions used in Bioconductor.", "biocViews": "Infrastructure", "URL": "https://bioconductor.org/packages/BiocGenerics", "BugReports": "https://github.com/Bioconductor/BiocGenerics/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Author": "The Bioconductor Dev Team", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Depends": ["R (>= 4.0.0)", "methods", "utils", "graphics", "stats"], "Imports": ["methods", "utils", "graphics", "stats"], "Suggests": ["Biobase", "S4Vectors", "IRanges", "GenomicRanges", "DelayedArray", "Biostrings", "Rsamtools", "AnnotationDbi", "affy", "affyPLM", "DESeq2", "flowClust", "MSnbase", "annotate", "RUnit"], "Collate": "S3-classes-as-S4-classes.R utils.R normarg-utils.R replaceSlots.R aperm.R append.R as.data.frame.R as.list.R as.vector.R cbind.R do.call.R duplicated.R eval.R Extremes.R format.R funprog.R get.R grep.R is.unsorted.R lapply.R mapply.R match.R mean.R nrow.R order.R paste.R rank.R rep.R row_colnames.R sets.R sort.R start.R subset.R t.R table.R tapply.R unique.R unlist.R unsplit.R relist.R var.R which.R which.min.R boxplot.R image.R density.R IQR.R mad.R residuals.R weights.R xtabs.R annotation.R combine.R dbconn.R dge.R dims.R fileName.R normalize.R Ontology.R organism_species.R path.R plotMA.R plotPCA.R score.R strand.R toTable.R type.R updateObject.R testPackage.R zzz.R", "git_url": "https://git.bioconductor.org/packages/BiocGenerics", "git_branch": "RELEASE_3_18", "git_last_commit": "5fd6dfe", "git_last_commit_date": "2023-10-31", "NeedsCompilation": "no"}, "BiocManager": {"Package": "BiocManager", "Version": "1.30.26", "Source": "Repository", "Title": "Access the Bioconductor Project Package Repository", "Description": "A convenient tool to install and update Bioconductor packages.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-5874-8148\")), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-3242-0582\")))", "Imports": ["utils"], "Suggests": ["BiocVersion", "BiocStyle", "remotes", "rmarkdown", "testthat", "withr", "curl", "knitr"], "URL": "https://bioconductor.github.io/BiocManager/", "BugReports": "https://github.com/Bioconductor/BiocManager/issues", "VignetteBuilder": "knitr", "License": "Artistic-2.0", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-5874-8148>), <PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-3242-0582>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "DBI": {"Package": "DBI", "Version": "1.2.3", "Source": "Repository", "Title": "R Database Interface", "Date": "2024-06-02", "Authors@R": "c( person(\"R Special Interest Group on Databases (R-SIG-DB)\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"R Consortium\", role = \"fnd\") )", "Description": "A database interface definition for communication between R and relational database management systems.  All classes in this package are virtual and need to be extended by the various R/DBMS implementations.", "License": "LGPL (>= 2.1)", "URL": "https://dbi.r-dbi.org, https://github.com/r-dbi/DBI", "BugReports": "https://github.com/r-dbi/DBI/issues", "Depends": ["methods", "R (>= 3.0.0)"], "Suggests": ["arrow", "blob", "covr", "DBItest", "dbplyr", "downlit", "dplyr", "glue", "hms", "knitr", "magrit<PERSON>", "nanoarrow (>= *******)", "RMariaDB", "rmarkdown", "rprojroot", "RSQLite (>= 1.1-2)", "testthat (>= 3.0.0)", "vctrs", "xml2"], "VignetteBuilder": "knitr", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/Needs/check": "r-dbi/DBItest", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Config/Needs/website": "r-dbi/DBItest, r-dbi/dbitemplate, adbi, AzureKusto, bigrquery, DatabaseConnector, dittodb, duckdb, implyr, lazysf, odbc, pool, RAthena, IMSMWU/RClickhouse, RH2, RJDBC, RMariaDB, RMySQL, RPostgres, RPostgreSQL, RPresto, RSQLite, sergeant, sparklyr, withr", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "R Special Interest Group on Databases (R-SIG-DB) [aut], <PERSON> [aut], <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), R Consortium [fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "R6": {"Package": "R6", "Version": "2.6.1", "Source": "Repository", "Title": "Encapsulated Classes with Reference Semantics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Creates classes with reference semantics, similar to R's built-in reference classes. Compared to reference classes, R6 classes are simpler and lighter-weight, and they are not built on S4 classes so they do not require the methods package. These classes allow public and private members, and they support inheritance, even when the classes are defined in different packages.", "License": "MIT + file LICENSE", "URL": "https://r6.r-lib.org, https://github.com/r-lib/R6", "BugReports": "https://github.com/r-lib/R6/issues", "Depends": ["R (>= 3.6)"], "Suggests": ["lobstr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2, microbenchmark, scales", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RSQLite": {"Package": "RSQLite", "Version": "2.4.3", "Source": "Repository", "Title": "SQLite Interface for R", "Date": "2025-08-01", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(c(\"<PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON> Richard\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(, \"SQLite Authors\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"R Consortium\", role = \"fnd\"), person(, \"RStu<PERSON>\", role = \"cph\") )", "Description": "Embeds the SQLite database engine in R and provides an interface compliant with the DBI package. The source for the SQLite engine (version 3.50.4) and for various extensions is included. System libraries will never be consulted because this package relies on static linking for the plugins it includes; this also ensures a consistent experience across all installations.", "License": "LGPL (>= 2.1)", "URL": "https://rsqlite.r-dbi.org, https://github.com/r-dbi/RSQLite", "BugReports": "https://github.com/r-dbi/RSQLite/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["bit64", "blob (>= 1.2.0)", "DBI (>= 1.2.0)", "memoise", "methods", "pkgconfig", "rlang"], "Suggests": ["callr", "cli", "DBItest (>= 1.8.0)", "decor", "gert", "gh", "hms", "knitr", "magrit<PERSON>", "rmarkdown", "rvest", "testthat (>= 3.0.0)", "withr", "xml2"], "LinkingTo": ["plogr (>= 0.2.0)", "cpp11 (>= 0.4.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "r-dbi/dbitemplate", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Collate": "'SQLiteConnection.R' 'SQLKeywords_SQLiteConnection.R' 'SQLiteDriver.R' 'SQLite.R' 'SQLiteResult.R' 'coerce.R' 'compatRowNames.R' 'copy.R' 'cpp11.R' 'datasetsDb.R' 'dbAppendTable_SQLiteConnection.R' 'dbBeginTransaction.R' 'dbBegin_SQLiteConnection.R' 'dbBind_SQLiteResult.R' 'dbClearResult_SQLiteResult.R' 'dbColumnInfo_SQLiteResult.R' 'dbCommit_SQLiteConnection.R' 'dbConnect_SQLiteConnection.R' 'dbConnect_SQLiteDriver.R' 'dbDataType_SQLiteConnection.R' 'dbDataType_SQLiteDriver.R' 'dbDisconnect_SQLiteConnection.R' 'dbExistsTable_SQLiteConnection_Id.R' 'dbExistsTable_SQLiteConnection_character.R' 'dbFetch_SQLiteResult.R' 'dbGetException_SQLiteConnection.R' 'dbGetInfo_SQLiteConnection.R' 'dbGetInfo_SQLiteDriver.R' 'dbGetPreparedQuery.R' 'dbGetPreparedQuery_SQLiteConnection_character_data.frame.R' 'dbGetRowCount_SQLiteResult.R' 'dbGetRowsAffected_SQLiteResult.R' 'dbGetStatement_SQLiteResult.R' 'dbHasCompleted_SQLiteResult.R' 'dbIsValid_SQLiteConnection.R' 'dbIsValid_SQLiteDriver.R' 'dbIsValid_SQLiteResult.R' 'dbListResults_SQLiteConnection.R' 'dbListTables_SQLiteConnection.R' 'dbQuoteIdentifier_SQLiteConnection_SQL.R' 'dbQuoteIdentifier_SQLiteConnection_character.R' 'dbReadTable_SQLiteConnection_character.R' 'dbRemoveTable_SQLiteConnection_character.R' 'dbRollback_SQLiteConnection.R' 'dbSendPreparedQuery.R' 'dbSendPreparedQuery_SQLiteConnection_character_data.frame.R' 'dbSendQuery_SQLiteConnection_character.R' 'dbUnloadDriver_SQLiteDriver.R' 'dbUnquoteIdentifier_SQLiteConnection_SQL.R' 'dbWriteTable_SQLiteConnection_character_character.R' 'dbWriteTable_SQLiteConnection_character_data.frame.R' 'db_bind.R' 'deprecated.R' 'export.R' 'fetch_SQLiteResult.R' 'import-standalone-check_suggested.R' 'import-standalone-purrr.R' 'initExtension.R' 'initRegExp.R' 'isSQLKeyword_SQLiteConnection_character.R' 'make.db.names_SQLiteConnection_character.R' 'pkgconfig.R' 'show_SQLiteConnection.R' 'sqlData_SQLiteConnection.R' 'table.R' 'transactions.R' 'utils.R' 'version.R' 'zzz.R'", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb] (for the included SQLite sources), <PERSON> [ctb] (for the included SQLite sources), <PERSON> [ctb] (for the included SQLite sources), SQLite Authors [ctb] (for the included SQLite sources), <PERSON> [ctb] (for the included SQLite sources), R Consortium [fnd], RStudio [cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "Rcpp": {"Package": "Rcpp", "Version": "1.1.0", "Source": "Repository", "Title": "Seamless R and C++ Integration", "Date": "2025-07-01", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\", comment = c(ORCID = \"0000-0003-0174-9868\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6403-5550\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"))", "Description": "The 'Rcpp' package provides R functions as well as C++ classes which offer a seamless integration of R and C++. Many R data types and objects can be mapped back and forth to C++ equivalents which facilitates both writing of new code as well as easier integration of third-party libraries. Documentation about 'Rcpp' is provided by several vignettes included in this package, via the 'Rcpp Gallery' site at <https://gallery.rcpp.org>, the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011, <doi:10.18637/jss.v040.i08>), the book by <PERSON><PERSON><PERSON><PERSON><PERSON> (2013, <doi:10.1007/978-1-4614-6868-4>) and the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2018, <doi:10.1080/00031305.2017.1375990>); see 'citation(\"Rcpp\")' for details.", "Imports": ["methods", "utils"], "Suggests": ["tinytest", "inline", "rbenchmark", "pkgKitten (>= 0.1.2)"], "URL": "https://www.rcpp.org, https://dirk.eddelbuettel.com/code/rcpp.html, https://github.com/RcppCore/Rcpp", "License": "GPL (>= 2)", "BugReports": "https://github.com/RcppCore/Rcpp/issues", "MailingList": "<EMAIL>", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0001-6419-907X>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0174-9868>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-2880-7407>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-6786-5453>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-6403-5550>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "Rhdf5lib": {"Package": "Rhdf5lib", "Version": "1.24.2", "Source": "Bioconductor", "Type": "Package", "Title": "hdf5 library as an R package", "Authors@R": "c( person( \"<PERSON>\", \"<PERSON>\",  role=c(\"ctb\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-7800-3848\") ), person( given = \"The HDF Group\", role = \"cph\" ))", "Description": "Provides C and C++ hdf5 libraries.", "License": "Artistic-2.0", "Copyright": "src/hdf5/COPYING", "LazyLoad": "true", "VignetteBuilder": "knitr", "Depends": ["R (>= 4.2.0)"], "Suggests": ["BiocStyle", "knitr", "rmarkdown", "tinytest", "mockery"], "URL": "https://github.com/grimbough/Rhdf5lib", "BugReports": "https://github.com/grimbough/Rhdf5lib", "SystemRequirements": "GNU make", "Encoding": "UTF-8", "biocViews": "Infrastructure", "RoxygenNote": "7.1.2", "git_url": "https://git.bioconductor.org/packages/Rhdf5lib", "git_branch": "RELEASE_3_18", "git_last_commit": "f4cab37", "git_last_commit_date": "2024-02-07", "Repository": "Bioconductor 3.18", "NeedsCompilation": "yes", "Author": "<PERSON> [ctb, cre] (<https://orcid.org/0000-0002-7800-3848>), The HDF Group [cph]", "Maintainer": "<PERSON> <<EMAIL>>"}, "S4Vectors": {"Package": "S4Vectors", "Version": "0.40.2", "Source": "Bioconductor", "Title": "Foundation of vector-like and list-like containers in Bioconductor", "Description": "The S4Vectors package defines the Vector and List virtual classes and a set of generic functions that extend the semantic of ordinary vectors and lists in R. Package developers can easily implement vector-like or list-like objects as concrete subclasses of Vector or List. In addition, a few low-level concrete subclasses of general interest (e.g. DataFrame, Rle, Factor, and Hits) are implemented in the S4Vectors package itself (many more are implemented in the IRanges package and in other Bioconductor infrastructure packages).", "biocViews": "Infrastructure, DataRepresentation", "URL": "https://bioconductor.org/packages/S4Vectors", "BugReports": "https://github.com/Bioconductor/S4Vectors/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment=\"Converted 'RleTricks' vignette from Sweave to RMarkdown.\"))", "Depends": ["R (>= 4.0.0)", "methods", "utils", "stats", "stats4", "BiocGenerics (>= 0.37.0)"], "Suggests": ["IRanges", "GenomicRanges", "SummarizedExperiment", "Matrix", "DelayedArray", "ShortRead", "graph", "data.table", "RUnit", "BiocStyle", "knitr"], "VignetteBuilder": "knitr", "Collate": "S4-utils.R show-utils.R utils.R normarg-utils.R bindROWS.R LLint-class.R isSorted.R subsetting-utils.R vector-utils.R integer-utils.R character-utils.R raw-utils.R eval-utils.R map_ranges_to_runs.R RectangularData-class.R Annotated-class.R DataFrame_OR_NULL-class.R Vector-class.R Vector-comparison.R Vector-setops.R Vector-merge.R Hits-class.R Hits-comparison.R Hits-setops.R Rle-class.R Rle-utils.R Factor-class.R List-class.R List-comparison.R splitAsList.R List-utils.R SimpleList-class.R HitsList-class.R DataFrame-class.R DataFrame-combine.R DataFrame-comparison.R DataFrame-utils.R DataFrameFactor-class.R TransposedDataFrame-class.R Pairs-class.R FilterRules-class.R stack-methods.R expand-methods.R aggregate-methods.R shiftApply-methods.R zzz.R", "git_url": "https://git.bioconductor.org/packages/S4Vectors", "git_branch": "RELEASE_3_18", "git_last_commit": "8cd5cb3", "git_last_commit_date": "2023-11-23", "Repository": "Bioconductor 3.18", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb] (Converted 'RleTricks' vignette from Sweave to RMarkdown.)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "VE2001NHTS": {"Package": "VE2001NHTS", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create 2001 NHTS datasets for model estimation", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre],", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "This package creates and saves 3 data frames that are used to  estimate various VisionEval models. These data frames are created from the publically available data from the 2001 National Household Travel Survey  (NHTS) augmented with data on metropolitan area freeway supply and transit supply. The package produces a data frames of household characteristics and travel, vehicle characteristics, household tours .", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "bzip2", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval"], "Suggests": ["knitr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.3.3", "NeedsCompilation": "no"}, "VEHouseholdTravel": {"Package": "VEHouseholdTravel", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Calculate Household Walk, Bike, and Transit Trips", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> <<EMAIL>> [cre]", "Maintainer": "<PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "Contains modules for predicting household average DVMT, trips by alternative modes (walking, biking, transit), household vehicle trips, and  percentage reduction in household DVMT a result of diversion of SOV DVMT to  slower vehicle modes (bike etc.)", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "bzip2", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "data.table", "pscl"], "Suggests": ["VE2001NHTS", "knitr"], "Encoding": "UTF-8", "VignetteBuilder": "knitr", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "NeedsCompilation": "no"}, "VEHouseholdVehicles": {"Package": "VEHouseholdVehicles", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Model household vehicles with driver and vehicle adjustments", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> <<EMAIL>> [cre]", "Maintainer": "<PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "This package contains modules which predict household vehicle ownership, including the number owned, body types, and ages. It also identifies car service accessibility by household (High & Low) and substitutes car service (e.g. car sharing or transportation network company) for owning a household vehicle for households that have a high level of car service availability and the cost of owning a car per mile of travel is higher than the cost per mile of using a car service. The cost of owning vehicles is calculated as a function of the vehicle age, type, and vehicle miles traveled.", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "bzip2", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "ordinal", "reshape2"], "Suggests": ["knitr", "VE2001NHTS"], "Encoding": "UTF-8", "VignetteBuilder": "knitr", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "NeedsCompilation": "no"}, "VELandUse": {"Package": "VELandUse", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create Land Use Attributes for VE", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> <<EMAIL>> [cre]", "Maintainer": "<PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "This package contains several modules which establish land use  characteristics of Bzones including employment by type, density, diversity, network design, and accessibility. Modules also identify the housing type and Bzone location of each household, create a Worker table and assign each worker to a Bzone job location, and identify whether each household is  located in an urbanized area or rural area. In addition, modules are included for processing several policy inputs related to land use that are applied at the Bzone level. These include parking, travel demand management programs, and car services.", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "bzip2", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "geosphere", "fields", "VESimHouseholds"], "Suggests": ["knitr", "VE2001NHTS"], "Encoding": "UTF-8", "VignetteBuilder": "knitr", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "NeedsCompilation": "no"}, "VEModel": {"Package": "VEModel", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Utilities for Managing VisionEval Models and Samples", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "None", "Description": "Contains R6 object management code providing functions and objects to set up VisionEval models, run them, and query or extract their output.", "License": "file LICENSE", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "R6", "stringr", "yaml", "futile.logger", "jsonlite", "parallelly", "future", "future.callr", "jrc", "methods", "DBI", "RSQLite", "data.table"], "Suggests": ["knitr", "pkgload", "markdown", "RMariaDB"], "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "Collate": "'environment.R' 'export.R' 'models.R' 'results.R' 'query.R' 'scenarios.R' 'zzz.R'", "NeedsCompilation": "no"}, "VEPowertrainsAndFuels": {"Package": "VEPowertrainsAndFuels", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Default Powertrains And Fuels for ODOT Adopted Plans scenario (xAP)", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre],", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO, ODOT", "Description": "Contains modules for inputting default powertrain and fuel characteristics and for allowing households to override some of the defaults with characteristics for their region. Calculates the carbon intensity of  fuels and assigns powertrains and powertrain characteristics to household  vehicles and car service vehicles. The default values are for the Oregon DOT Adopted Plans scenario as of February 25, 2020. The ICEV MPG values by model year were adjusted 6% lower to better match validation targets for fuel consumption. This is the \"xAP\" version, 2020-07-24.", "License": "file LICENSE", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "VEHouseholdTravel"], "Suggests": ["knitr", "VE2001NHTS"], "Encoding": "UTF-8", "VignetteBuilder": "knitr", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "Collate": "'LoadDefaultValues.R' 'Initialize.R' 'CalculateCarbonIntensity.R' 'AssignHhVehiclePowertrain.R'", "NeedsCompilation": "no"}, "VESimHouseholds": {"Package": "VESimHouseholds", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create Simulated Households with Attributes for VE", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre],", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "This package contains several modules which create synthetic households with attributes of persons by age group, income, workers, and life cycle. It also prepares a data frame of model estimation data from Census PUMS data that is used to estimate models in this package and the VELandUse package.", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "bzip2", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval"], "Suggests": ["knitr", "rmarkdown"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "Collate": "'CreateEstimationDatasets.R' 'CreateHouseholds.R' 'PredictWorkers.R' 'PredictIncome.R' 'AssignLifeCycle.R'", "NeedsCompilation": "no"}, "VESimLandUse": {"Package": "VESimLandUse", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create simulated Bzones (SimBzones) with attributes", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre],", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO, ODOT", "Description": "This package contains several modules which create simulated  Bzones (SimBzones), assign households and jobs to the SimBzones, attribute the SimBzones with a number of land use characteristics, and assign policy inputs related to SimBzones.", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "xz", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "plot3D", "VELandUse", "VESimLandUseData"], "Suggests": ["knitr", "rmarkdown"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "Collate": "CreateSimBzoneModels.R Initialize.R CreateSimBzones.R SimulateHousing.R SimulateEmployment.R Simulate4DMeasures.R SimulateUrbanMixMeasure.R AssignCarSvcAvailability.R AssignDemandManagement.R AssignParkingRestrictions.R Finalize.R", "NeedsCompilation": "no"}, "VESimLandUseData": {"Package": "VESimLandUseData", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create dataset used for estimating land use simulation models", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre],", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "Oregon Department of Transportation", "Description": "This package processes data from several sources to produce a dataset that is used to estimate several models for synthesizing Bzones and their likely land use attributes. Datasets from the U.S. Census 2015 American Community Survey, EPA Smart Location Database (SLD), and National Transit Database (NTD) are combined to produce this dataset.", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "bzip2", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "tidycensus", "stringr"], "Suggests": ["knitr", "rmarkdown"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.3.3", "NeedsCompilation": "no"}, "VESimTransportSupply": {"Package": "VESimTransportSupply", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create Transportation System Attributes for VE", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "This package contains several modules which attribute the metropolitan area and Bzones with transportation supply characteristics including freeway lane miles, public transportation revenue miles, neighborhood transit accessibility, and calculation of transit vehicle miles by vehicle type from transit revenue miles. This package is intended for use in conjunction with the VESimLandUse package.", "License": "file LICENSE", "LazyData": "TRUE", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "VESimLandUse", "VETransportSupply"], "Suggests": ["knitr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "NeedsCompilation": "no"}, "VESnapshot": {"Package": "VESnapshot", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Sample Module to take a snapshot of Datastore fields", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "None", "Description": "Can be used in any VisionEval model to copy a Datastore field to a different field in the Datastore.", "License": "file LICENSE", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval"], "Suggests": ["knitr", "VEModel"], "Encoding": "UTF-8", "VignetteBuilder": "knitr", "RoxygenNote": "7.2.3", "Collate": "parameters.R Snapshot.R Dynamic.R", "NeedsCompilation": "no"}, "VESyntheticFirms": {"Package": "VESyntheticFirms", "Version": "3.1.1", "Source": "unknown", "TYPE": "Package", "Title": "VisionEval Synthetic Firms module for RPAT", "Date": "2024-01-23", "Author": "<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "VisionEval Synthetic Firms module.", "License": "file LICENSE", "LazyData": "TRUE", "LazyDataCompression": "bzip2", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "reshape"], "Suggests": ["knitr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "Collate": "'CreateBaseSyntheticFirms.R' 'CreateFutureSyntheticFirms.R'", "NeedsCompilation": "no"}, "VETransportSupply": {"Package": "VETransportSupply", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create Transportation System Attributes for VE", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> <<EMAIL>> [cre]", "Maintainer": "<PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "This package contains several modules which attribute the metropolitan area and Bzones with transportation supply characteristics including freeway lane miles, public transportation revenue miles, neighborhood transit accessibility, and calculation of transit vehicle miles by vehicle type from transit revenue miles.", "License": "file LICENSE", "LazyData": "TRUE", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval"], "Suggests": ["knitr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "NeedsCompilation": "no"}, "VETransportSupplyUse": {"Package": "VETransportSupplyUse", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Create Transport Supply Utilization Characteristics for VE", "Date": "2024-01-23", "Author": "<PERSON><PERSON><PERSON> <<EMAIL>> [cre], <PERSON> <<EMAIL>> [cre],  <PERSON> <<EMAIL>> [auth]", "Maintainer": "<PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "This package contains modules that creates attributes for utilization of transportation supplies.", "License": "file LICENSE", "LazyData": "TRUE", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval"], "Suggests": ["knitr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "Collate": "'CalculateCongestionBase.R' 'CalculateCongestionFuture.R' 'CalculateCongestionPolicy.R'", "NeedsCompilation": "no"}, "VETravelPerformance": {"Package": "VETravelPerformance", "Version": "3.1.1", "Source": "unknown", "Type": "Package", "Title": "Balances cost and travel and calculates travel performance measures", "Date": "2024-01-23", "Author": "<PERSON> [aut, cre],", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO", "Description": "Contains modules for calculating fuel consumption, emissions,  roadway speeds and delay, and vehicle use costs. Working together, these modules also balance household travel costs and the amount of household DVMT. Household DVMT is split among household vehicles and between household vehicles and car services based on composite costs (out-of-pocket and travel time). Calculates fuel consumption (including electricity) and greenhouse gas emissions for households, light-duty commercial service vehicles, heavy trucks, and public transit modes.", "License": "file LICENSE", "LazyData": "TRUE", "Depends": ["R (>= 4.0.0)"], "Imports": ["visioneval", "dplyr", "rlang", "magrit<PERSON>"], "Suggests": ["VEPowertrainsAndFuels", "VEHouseholdTravel"], "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "NeedsCompilation": "no"}, "brio": {"Package": "brio", "Version": "1.1.5", "Source": "Repository", "Title": "Basic R Input Output", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Functions to handle basic input output, these functions always read and write UTF-8 (8-bit Unicode Transformation Format) files and provide more explicit control over line endings.", "License": "MIT + file LICENSE", "URL": "https://brio.r-lib.org, https://github.com/r-lib/brio", "BugReports": "https://github.com/r-lib/brio/issues", "Depends": ["R (>= 3.6)"], "Suggests": ["covr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "callr": {"Package": "callr", "Version": "3.7.6", "Source": "Repository", "Title": "Call R from R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\"), comment = c(ORCID = \"0000-0001-7098-9676\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"Ascent Digital Services\", role = c(\"cph\", \"fnd\")) )", "Description": "It is sometimes useful to perform a computation in a separate R process, without affecting the current R process at all.  This packages does exactly that.", "License": "MIT + file LICENSE", "URL": "https://callr.r-lib.org, https://github.com/r-lib/callr", "BugReports": "https://github.com/r-lib/callr/issues", "Depends": ["R (>= 3.4)"], "Imports": ["processx (>= 3.6.1)", "R6", "utils"], "Suggests": ["asciicast (>= 2.3.1)", "cli (>= 1.1.0)", "mockery", "ps", "rprojroot", "spelling", "testthat (>= 3.2.0)", "withr (>= 2.3.0)"], "Config/Needs/website": "r-lib/asciicast, glue, htmlwidgets, igraph, tibble, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.1.9000", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0001-7098-9676>), <PERSON> [aut], Posit Software, PBC [cph, fnd], Ascent Digital Services [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "classInt": {"Package": "classInt", "Version": "0.4-11", "Source": "Repository", "Date": "2025-01-06", "Title": "Choose Univariate Class Intervals", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<PERSON>.<EMAIL>\", comment=c(ORCID=\"0000-0003-2392-6140\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-5759-428X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"Hernangómez\", role=\"ctb\", comment=c(ORCID=\"0000-0001-8457-4658\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0001-9910-865X\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment =c(ORCID=\"0000-0002-6802-4290\")))", "Depends": ["R (>= 2.2)"], "Imports": ["grDevices", "stats", "graphics", "e1071", "class", "KernSmooth"], "Suggests": ["spData (>= *******)", "units", "knitr", "rmarkdown", "tinytest"], "NeedsCompilation": "yes", "Description": "Selected commonly used methods for choosing univariate class intervals for mapping or other graphics purposes.", "License": "GPL (>= 2)", "URL": "https://r-spatial.github.io/classInt/, https://github.com/r-spatial/classInt/", "BugReports": "https://github.com/r-spatial/classInt/issues/", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "VignetteBuilder": "knitr", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-2392-6140>), <PERSON> [ctb] (<https://orcid.org/0000-0002-5759-428X>), <PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0001-8457-4658>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0001-9910-865X>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-6802-4290>)", "Maintainer": "<PERSON> <<PERSON>.B<PERSON><EMAIL>>", "Repository": "CRAN"}, "cli": {"Package": "cli", "Version": "3.6.5", "Source": "Repository", "Title": "Helpers for Developing Command Line Interfaces", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"g<PERSON><PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A suite of tools to build attractive command line interfaces ('CLIs'), from semantic elements: headings, lists, alerts, paragraphs, etc. Supports custom themes via a 'CSS'-like language. It also contains a number of lower level 'CLI' elements: rules, boxes, trees, and 'Unicode' symbols with 'ASCII' alternatives. It support ANSI colors and text styles as well.", "License": "MIT + file LICENSE", "URL": "https://cli.r-lib.org, https://github.com/r-lib/cli", "BugReports": "https://github.com/r-lib/cli/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "crayon", "digest", "glue (>= 1.6.0)", "grDevices", "htmltools", "htmlwidgets", "knitr", "methods", "processx", "ps (>= 1.3.4.9000)", "rlang (>= 1.0.2.9003)", "rmarkdown", "rprojroot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= 3.2.0)", "tibble", "whoami", "withr"], "Config/Needs/website": "r-lib/asciicast, bench, brio, cpp11, decor, desc, fansi, prettyunits, sessioninfo, tidyverse/tidytemplate, usethis, vctrs", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "curl": {"Package": "curl", "Version": "7.0.0", "Source": "Repository", "Type": "Package", "Title": "A Modern and Flexible Web Client for R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = \"cph\"))", "Description": "Bindings to 'libcurl' <https://curl.se/libcurl/> for performing fully configurable HTTP/FTP requests where responses can be processed in memory, on disk, or streaming via the callback or connection interfaces. Some knowledge of 'libcurl' is recommended; for a more-user-friendly web client see the  'httr2' package which builds on this package with http specific tools and logic.", "License": "MIT + file LICENSE", "SystemRequirements": "libcurl (>= 7.73): libcurl-devel (rpm) or libcurl4-openssl-dev (deb)", "URL": "https://jeroen.r-universe.dev/curl", "BugReports": "https://github.com/jeroen/curl/issues", "Suggests": ["spelling", "testthat (>= 1.0.0)", "knitr", "jsonlite", "later", "rmarkdown", "httpuv (>= 1.4.4)", "webutils"], "VignetteBuilder": "knitr", "Depends": ["R (>= 3.0.0)"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], Posit Software, PBC [cph]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "desc": {"Package": "desc", "Version": "1.4.3", "Source": "Repository", "Title": "Manipulate DESCRIPTION Files", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"csar<PERSON>.<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-2815-0399\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Tools to read, write, create, and manipulate DESCRIPTION files.  It is intended for packages that create or manipulate other packages.", "License": "MIT + file LICENSE", "URL": "https://desc.r-lib.org/, https://github.com/r-lib/desc", "BugReports": "https://github.com/r-lib/desc/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli", "R6", "utils"], "Suggests": ["callr", "covr", "gh", "spelling", "testthat", "whoami", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "Collate": "'assertions.R' 'authors-at-r.R' 'built.R' 'classes.R' 'collate.R' 'constants.R' 'deps.R' 'desc-package.R' 'description.R' 'encoding.R' 'find-package-root.R' 'latex.R' 'non-oo-api.R' 'package-archives.R' 'read.R' 'remotes.R' 'str.R' 'syntax_checks.R' 'urls.R' 'utils.R' 'validate.R' 'version.R'", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-2815-0399>), Posit Software, PBC [cph, fnd]", "Repository": "CRAN"}, "devtools": {"Package": "devtools", "Version": "2.4.6", "Source": "Repository", "Title": "Tools to Make Developing R Packages Easier", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Collection of package development tools.", "License": "MIT + file LICENSE", "URL": "https://devtools.r-lib.org/, https://github.com/r-lib/devtools", "BugReports": "https://github.com/r-lib/devtools/issues", "Depends": ["R (>= 4.1)", "usethis (>= 3.2.1)"], "Imports": ["cli (>= 3.6.5)", "desc (>= 1.4.3)", "ellipsis (>= 0.3.2)", "fs (>= 1.6.6)", "lifecycle (>= 1.0.4)", "memoise (>= 2.0.1)", "miniUI (>= 0.1.2)", "pkgbuild (>= 1.4.8)", "pkgdown (>= 2.1.3)", "pkgload (>= 1.4.1)", "profvis (>= 0.4.0)", "rcmdcheck (>= 1.4.0)", "remotes (>= 2.5.0)", "rlang (>= 1.1.6)", "roxygen2 (>= 7.3.3)", "rversions (>= 2.1.2)", "sessioninfo (>= 1.2.3)", "stats", "testthat (>= 3.2.3)", "tools", "urlchecker (>= 1.0.1)", "utils", "withr (>= 3.0.2)"], "Suggests": ["BiocManager (>= 1.30.18)", "callr (>= 3.7.1)", "covr (>= 3.5.1)", "curl (>= 4.3.2)", "digest (>= 0.6.29)", "DT (>= 0.23)", "foghorn (>= 1.4.2)", "gh (>= 1.3.0)", "gmailr (>= 1.0.1)", "httr (>= 1.4.3)", "knitr (>= 1.39)", "lintr (>= 3.0.0)", "MASS", "mockery (>= 0.4.3)", "pingr (>= 2.0.1)", "rhub (>= 1.1.1)", "rmarkdown (>= 2.14)", "rstudi<PERSON><PERSON> (>= 0.13)", "spelling (>= 2.2)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-6983-2759>), Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "diffobj": {"Package": "diffob<PERSON>", "Version": "0.3.6", "Source": "Repository", "Type": "Package", "Title": "Diffs for R Objects", "Description": "Generate a colorized diff of two R objects for an intuitive visualization of their differences.", "Authors@R": "c( person( \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\")), person( \"<PERSON>\", \"<PERSON>\", email=\"<EMAIL>\", role=c(\"ctb\", \"cph\"), comment=\"Original C implementation of Myers Diff Algorithm\"))", "Depends": ["R (>= 3.1.0)"], "License": "GPL-2 | GPL-3", "URL": "https://github.com/brodieG/diffobj", "BugReports": "https://github.com/brodieG/diffobj/issues", "RoxygenNote": "7.2.3", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Suggests": ["knitr", "rmarkdown"], "Collate": "'capt.R' 'options.R' 'pager.R' 'check.R' 'finalizer.R' 'misc.R' 'html.R' 'styles.R' 's4.R' 'core.R' 'diff.R' 'get.R' 'guides.R' 'hunks.R' 'layout.R' 'myerssimple.R' 'rdiff.R' 'rds.R' 'set.R' 'subset.R' 'summmary.R' 'system.R' 'text.R' 'tochar.R' 'trim.R' 'word.R'", "Imports": ["crayon (>= 1.3.2)", "tools", "methods", "utils", "stats"], "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb, cph] (Original C implementation of Myers Diff Algorithm)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "dotCall64": {"Package": "dotCall64", "Version": "1.2", "Source": "Repository", "Type": "Package", "Title": "Enhanced Foreign Function Interface Supporting Long Vectors", "Date": "2024-10-03", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"),  person(\"<PERSON><PERSON><PERSON>\", \"Gerber\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8545-5263\")), person(\"<PERSON><PERSON><PERSON>\", \"Furrer\", role = c(\"cre\", \"ctb\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-6319-2332\")))", "Description": "Provides .C64(), which is an enhanced version of .C() and .Fortran() from the foreign function interface. .C64() supports long vectors, arguments of type 64-bit integer, and provides a mechanism to avoid unnecessary copies of read-only and write-only arguments. This makes it a convenient and fast interface to C/C++ and Fortran code.", "License": "GPL (>= 2)", "URL": "https://git.math.uzh.ch/reinhard.furrer/dotCall64", "BugReports": "https://git.math.uzh.ch/reinhard.furrer/dotCall64/-/issues", "Depends": ["R (>= 4.0)"], "Suggests": ["microbenchmark", "RhpcBLASctl", "RColorBrewer", "roxygen2", "spam", "testthat"], "Collate": "'vector_dc.R' 'dotCall64.R' 'zzz.R'", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-8545-5263>), <PERSON><PERSON><PERSON> [cre, ctb] (<https://orcid.org/0000-0002-6319-2332>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "downlit": {"Package": "downlit", "Version": "0.4.4", "Source": "Repository", "Title": "Syntax Highlighting and Automatic Linking", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Syntax highlighting of R code, specifically designed for the needs of 'RMarkdown' packages like 'pkgdown', 'hugodown', and 'bookdown'. It includes linking of function calls to their documentation on the web, and automatic translation of ANSI escapes in output to the equivalent HTML.", "License": "MIT + file LICENSE", "URL": "https://downlit.r-lib.org/, https://github.com/r-lib/downlit", "BugReports": "https://github.com/r-lib/downlit/issues", "Depends": ["R (>= 4.0.0)"], "Imports": ["brio", "desc", "digest", "evaluate", "fansi", "memoise", "rlang", "vctrs", "withr", "yaml"], "Suggests": ["covr", "htmltools", "jsonlite", "MASS", "MassSpecWavelet", "pkgload", "rmarkdown", "testthat (>= 3.0.0)", "xml2"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "e1071": {"Package": "e1071", "Version": "1.7-16", "Source": "Repository", "Title": "Misc Functions of the Department of Statistics, Probability Theory Group (Formerly: E1071), TU Wien", "Imports": ["graphics", "grDevices", "class", "stats", "methods", "utils", "proxy"], "Suggests": ["cluster", "mlbench", "nnet", "randomForest", "rpart", "SparseM", "xtable", "Matrix", "MASS", "slam"], "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<PERSON><PERSON>@R-project.org\", comment = c(ORCID = \"0000-0002-5196-3048\")),     person(given = \"Evgenia\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"aut\",\"cph\")), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\", email = \"<PERSON>.Horn<PERSON>@R-project.org\", comment = c(ORCID = \"0000-0003-4198-9911\")), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON><PERSON>-<PERSON>\", family = \"<PERSON>\", role = c(\"ctb\",\"cph\"), comment = \"libsvm C++-code\"), person(given = \"<PERSON><PERSON>-<PERSON>\", family = \"<PERSON>\", role = c(\"ctb\",\"cph\"), comment = \"libsvm C++-code\"))", "Description": "Functions for latent class analysis, short time Fourier transform, fuzzy clustering, support vector machines, shortest path computation, bagged clustering, naive <PERSON><PERSON> classifier, generalized k-nearest neighbour ...", "License": "GPL-2 | GPL-3", "LazyLoad": "yes", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-5196-3048>), <PERSON><PERSON><PERSON><PERSON> [aut, cph], <PERSON> [aut] (<https://orcid.org/0000-0003-4198-9911>), <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [ctb, cph] (libsvm C++-code), <PERSON><PERSON><PERSON><PERSON> [ctb, cph] (libsvm C++-code)", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "CRAN"}, "evaluate": {"Package": "evaluate", "Version": "1.0.5", "Source": "Repository", "Type": "Package", "Title": "Parsing and Evaluation Tools that Provide More Details than the Default", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Parsing and evaluation tools that make it easy to recreate the command line behaviour of R.", "License": "MIT + file LICENSE", "URL": "https://evaluate.r-lib.org/, https://github.com/r-lib/evaluate", "BugReports": "https://github.com/r-lib/evaluate/issues", "Depends": ["R (>= 3.6.0)"], "Suggests": ["callr", "covr", "ggplot2 (>= 3.3.6)", "lattice", "methods", "pkgload", "ragg (>= 1.4.0)", "rlang (>= 1.1.5)", "knitr", "testthat (>= 3.0.0)", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0645-5666>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fields": {"Package": "fields", "Version": "17.1", "Source": "Repository", "Date": "2025-09-03", "Title": "Tools for Spatial Data", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"douglasny<PERSON><EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"Furrer\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"rider<PERSON><PERSON><EMAIL>\") )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "For curve, surface and function fitting with an emphasis on splines, spatial data, geostatistics, and spatial statistics. The major methods include  Gaussian spatial process prediction (known as Kriging), cubic and thin plate splines, and compactly supported covariance functions for large data sets. The spline and spatial process methods are supported by functions that can determine the smoothing parameter (nugget and sill variance) and other covariance function parameters by cross validation and also by  maximum likelihood. For spatial process prediction there is an easy to use function that also estimates the correlation scale (range parameter).  A major feature is that any covariance function implemented in R and following a simple format can be used for spatial prediction. As included are fast approximations for prediction and conditional simulation for larger data sets. There are also many useful functions for plotting and working with spatial data as images. This package also contains an implementation of sparse matrix methods for large spatial data sets based the  R sparse matrix package spam. Use help(fields) to get started and for an overview. All package graphics functions focus on  extending base R graphics and are easy to interpret and modify. The fields source code is deliberately commented and provides useful explanations of numerical details as a companion to the manual pages. The commented source code can be viewed by expanding the source code version of this package and looking in the R subdirectory. The reference for fields can be generated by the citation function in R and has DOI <doi:10.5065/D6W957CT>. Development of this package was supported in part by the National Science Foundation  Grant 1417857,  the National Center for Atmospheric Research, and Colorado School of Mines. See the Fields URL for a vignette on using this package and some background on spatial statistics.", "License": "GPL (>= 2)", "URL": "https://github.com/dnychka/fieldsRPackage", "Depends": ["R (>= 4.0.0)", "methods", "spam", "viridisLite", "RColorBrewer"], "Imports": ["maps"], "Suggests": ["map<PERSON><PERSON>j"], "NeedsCompilation": "yes", "Repository": "CRAN", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut]"}, "formatR": {"Package": "formatR", "Version": "1.14", "Source": "Repository", "Type": "Package", "Title": "Format R Code Automatically", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Provides a function tidy_source() to format R source code. Spaces and indent will be added to the code automatically, and comments will be preserved under certain conditions, so that R code will be more human-readable and tidy. There is also a Shiny app as a user interface in this package (see tidy_app()).", "Depends": ["R (>= 3.2.3)"], "Suggests": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiny", "testit", "rmarkdown", "knitr"], "License": "GPL", "URL": "https://github.com/yihui/formatR", "BugReports": "https://github.com/yihui/formatR/issues", "VignetteBuilder": "knitr", "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fs": {"Package": "fs", "Version": "1.6.6", "Source": "Repository", "Title": "Cross-Platform File System Operations Based on 'libuv'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"libuv project contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Joyent, Inc. and other Node contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A cross-platform interface to file system operations, built on top of the 'libuv' C library.", "License": "MIT + file LICENSE", "URL": "https://fs.r-lib.org, https://github.com/r-lib/fs", "BugReports": "https://github.com/r-lib/fs/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "crayon", "knitr", "pillar (>= 1.0.0)", "rmarkdown", "spelling", "testthat (>= 3.0.0)", "tibble (>= 1.1.0)", "vctrs (>= 0.3.0)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "SystemRequirements": "GNU make", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], libuv project contributors [cph] (libuv library), Joyent, Inc. and other Node contributors [cph] (libuv library), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "futile.logger": {"Package": "futile.logger", "Version": "1.4.3", "Source": "Repository", "Type": "Package", "Title": "A Logging Utility for R", "Date": "2016-07-10", "Author": "<PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 3.0.0)"], "Imports": ["utils", "lambda.r (>= 1.1.0)", "futile.options"], "Suggests": ["testthat", "jsonlite"], "Description": "Provides a simple yet powerful logging utility. Based loosely on log4j, futile.logger takes advantage of R idioms to make logging a convenient and easy to use replacement for cat and print statements.", "License": "LGPL-3", "LazyLoad": "yes", "NeedsCompilation": "no", "ByteCompile": "yes", "Collate": "'options.R' 'appender.R' 'constants.R' 'layout.R' 'logger.R' 'scat.R' 'futile.logger-package.R'", "RoxygenNote": "5.0.1", "Repository": "CRAN"}, "futile.options": {"Package": "futile.options", "Version": "1.0.1", "Source": "Repository", "Type": "Package", "Title": "Futile Options Management", "Date": "2018-04-20", "Author": "<PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 2.8.0)"], "Description": "A scoped options management framework. Used in other packages.", "License": "LGPL-3", "LazyLoad": "yes", "NeedsCompilation": "no", "Repository": "CRAN"}, "generics": {"Package": "generics", "Version": "0.1.4", "Source": "Repository", "Title": "Common S3 Generics not Provided by Base R Methods Related to Model Fitting", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"https://ror.org/03wc8by49\")) )", "Description": "In order to reduce potential package dependencies and conflicts, generics provides a number of commonly used S3 generics.", "License": "MIT + file LICENSE", "URL": "https://generics.r-lib.org, https://github.com/r-lib/generics", "BugReports": "https://github.com/r-lib/generics/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "pkgload", "testthat (>= 3.0.0)", "tibble", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "geosphere": {"Package": "geosphere", "Version": "1.5-20", "Source": "Repository", "Type": "Package", "Title": "Spherical Trigonometry", "Date": "2024-10-02", "LinkingTo": ["Rcpp"], "Imports": ["Rcpp", "sp"], "Depends": ["R (>= 3.0.0)"], "Suggests": ["methods", "raster", "terra"], "Authors@R": "c( person(\"<PERSON>\", \"Hij<PERSON>\", role = c(\"cre\", \"aut\"),  email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment=\"GeographicLib\"),\t person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"))", "Description": "Spherical trigonometry for geographic applications. That is, compute distances and related measures for angular (longitude/latitude) locations.", "BugReports": "https://github.com/rspatial/geosphere/issues/", "License": "GPL (>= 3)", "LazyLoad": "yes", "NeedsCompilation": "yes", "Author": "<PERSON> [cre, aut], <PERSON> [ctb] (GeographicLib), <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "glue": {"Package": "glue", "Version": "1.8.0", "Source": "Repository", "Title": "Interpreted String Literals", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "An implementation of interpreted string literals, inspired by Python's Literal String Interpolation <https://www.python.org/dev/peps/pep-0498/> and Docstrings <https://www.python.org/dev/peps/pep-0257/> and <PERSON>'s Triple-Quoted String Literals <https://docs.julialang.org/en/v1.3/manual/strings/#Triple-Quoted-String-Literals-1>.", "License": "MIT + file LICENSE", "URL": "https://glue.tidyverse.org/, https://github.com/tidyverse/glue", "BugReports": "https://github.com/tidyverse/glue/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["crayon", "DBI (>= 1.2.0)", "dplyr", "knitr", "magrit<PERSON>", "rlang", "rmarkdown", "RSQLite", "testthat (>= 3.2.0)", "vctrs (>= 0.3.0)", "waldo (>= 0.5.3)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "bench, forcats, ggbeeswarm, ggplot2, <PERSON><PERSON>utils, rprintf, tidyr, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "here": {"Package": "here", "Version": "1.0.2", "Source": "Repository", "Title": "A Simpler Way to Find Your Files", "Date": "2025-09-06", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-6983-2759\")))", "Description": "Constructs paths to your project's files. Declare the relative path of a file within your project with 'i_am()'. Use the 'here()' function as a drop-in replacement for 'file.path()', it will always locate the files relative to your project root.", "License": "MIT + file LICENSE", "URL": "https://here.r-lib.org/, https://github.com/r-lib/here", "BugReports": "https://github.com/r-lib/here/issues", "Imports": ["rprojroot (>= 2.1.0)"], "Suggests": ["conflicted", "covr", "fs", "knitr", "palmerpenguins", "plyr", "readr", "rlang", "rmarkdown", "testthat", "uuid", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.3.9000", "Config/testthat/edition": "3", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-6983-2759>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "highr": {"Package": "highr", "Version": "0.11", "Source": "Repository", "Type": "Package", "Title": "Syntax Highlighting for R Source Code", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Description": "Provides syntax highlighting for R source code. Currently it supports LaTeX and HTML output. Source code of other languages is supported via <PERSON>'s highlight package (<https://gitlab.com/saalen/highlight>).", "Depends": ["R (>= 3.3.0)"], "Imports": ["xfun (>= 0.18)"], "Suggests": ["knitr", "markdown", "testit"], "License": "GPL", "URL": "https://github.com/yihui/highr", "BugReports": "https://github.com/yihui/highr/issues", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jrc": {"Package": "jrc", "Version": "0.6.0", "Source": "Repository", "Type": "Package", "Title": "Exchange Commands Between R and 'JavaScript'", "Date": "2023-08-22", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\") )", "Description": "An 'httpuv' based bridge between R and 'JavaScript'. Provides an easy way to exchange commands and data between a web page and a currently running R session.", "License": "GPL-3", "Imports": ["httpuv", "jsonlite", "utils", "stringr", "stringi", "mime", "R6", "<PERSON><PERSON>utils"], "RoxygenNote": "7.2.3", "URL": "https://github.com/anders-biostat/jrc", "BugReports": "https://github.com/anders-biostat/jrc/issues", "Suggests": ["testthat"], "Language": "en-GB", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut]", "Maintainer": "<PERSON><PERSON><PERSON> <s.ov<PERSON><PERSON>@zmbh.uni-heidelberg.de>", "Repository": "CRAN"}, "jsonlite": {"Package": "jsonlite", "Version": "2.0.0", "Source": "Repository", "Title": "A Simple and Robust JSON Parser and Generator for R", "License": "MIT + file LICENSE", "Depends": ["methods"], "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"cph\", comment=\"author of bundled libyajl\"))", "URL": "https://jeroen.r-universe.dev/jsonlite https://arxiv.org/abs/1403.2805", "BugReports": "https://github.com/jeroen/jsonlite/issues", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "VignetteBuilder": "knitr, R.rsp", "Description": "A reasonably fast JSON parser and generator, optimized for statistical  data and the web. Offers simple, flexible tools for working with JSON in R, and is particularly powerful for building pipelines and interacting with a web API.  The implementation is based on the mapping described in the vignette (Ooms, 2014). In addition to converting JSON data from/to R objects, 'jsonlite' contains  functions to stream, validate, and prettify JSON data. The unit tests included  with the package verify that all edge cases are encoded and decoded consistently  for use with dynamic data in systems and applications.", "Suggests": ["httr", "vctrs", "testthat", "knitr", "rmarkdown", "R.rsp", "sf"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], <PERSON> [cph] (author of bundled libyajl)", "Repository": "CRAN"}, "knitr": {"Package": "knitr", "Version": "1.50", "Source": "Repository", "Type": "Package", "Title": "A General-Purpose Package for Dynamic Report Generation in R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8335-495X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>t\", \"<PERSON><PERSON>rov\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>vie<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>-<PERSON>\", role = \"ctb\"), person(\"David\", \"Robinson\", role = \"ctb\"), person(\"Doug\", \"Hemken\", role = \"ctb\"), person(\"Duncan\", \"Murdoch\", role = \"ctb\"), person(\"Elio\", \"Campitelli\", role = \"ctb\"), person(\"Ellis\", \"Hughes\", role = \"ctb\"), person(\"Emily\", \"Riederer\", role = \"ctb\"), person(\"Fabian\", \"Hirschmann\", role = \"ctb\"), person(\"Fitch\", \"Simeon\", role = \"ctb\"), person(\"Forest\", \"Fang\", role = \"ctb\"), person(c(\"Frank\", \"E\", \"Harrell\", \"Jr\"), role = \"ctb\", comment = \"the Sweavel package at inst/misc/Sweavel.sty\"), person(\"Garrick\", \"Aden-Buie\", role = \"ctb\"), person(\"Gregoire\", \"Detrez\", role = \"ctb\"), person(\"Hadley\", \"Wickham\", role = \"ctb\"), person(\"Hao\", \"Zhu\", role = \"ctb\"), person(\"Heewon\", \"Jeon\", role = \"ctb\"), person(\"Henrik\", \"Bengtsson\", role = \"ctb\"), person(\"Hiroaki\", \"Yutani\", role = \"ctb\"), person(\"Ian\", \"Lyttle\", role = \"ctb\"), person(\"Hodges\", \"Daniel\", role = \"ctb\"), person(\"Jacob\", \"Bien\", role = \"ctb\"), person(\"Jake\", \"Burkhead\", role = \"ctb\"), person(\"James\", \"Manton\", role = \"ctb\"), person(\"Jared\", \"Lander\", role = \"ctb\"), person(\"Jason\", \"Punyon\", role = \"ctb\"), person(\"Javier\", \"Luraschi\", role = \"ctb\"), person(\"Jeff\", \"Arnold\", role = \"ctb\"), person(\"Jenny\", \"Bryan\", role = \"ctb\"), person(\"Jeremy\", \"Ashkenas\", role = c(\"ctb\", \"cph\"), comment = \"the CSS file at inst/misc/docco-classic.css\"), person(\"Jeremy\", \"Stephens\", role = \"ctb\"), person(\"Jim\", \"Hester\", role = \"ctb\"), person(\"Joe\", \"Cheng\", role = \"ctb\"), person(\"Johannes\", \"Ranke\", role = \"ctb\"), person(\"John\", \"Honaker\", role = \"ctb\"), person(\"John\", \"Muschelli\", role = \"ctb\"), person(\"Jonathan\", \"Keane\", role = \"ctb\"), person(\"JJ\", \"Allaire\", role = \"ctb\"), person(\"Johan\", \"Toloe\", role = \"ctb\"), person(\"Jonathan\", \"Sidi\", role = \"ctb\"), person(\"Joseph\", \"Larmarange\", role = \"ctb\"), person(\"Julien\", \"Barnier\", role = \"ctb\"), person(\"Kaiyin\", \"Zhong\", role = \"ctb\"), person(\"Kamil\", \"Slowikowski\", role = \"ctb\"), person(\"Karl\", \"Forner\", role = \"ctb\"), person(c(\"Kevin\", \"K.\"), \"Smith\", role = \"ctb\"), person(\"Kirill\", \"Mueller\", role = \"ctb\"), person(\"Kohske\", \"Takahashi\", role = \"ctb\"), person(\"Lorenz\", \"Walthert\", role = \"ctb\"), person(\"Lucas\", \"Gallindo\", role = \"ctb\"), person(\"Marius\", \"Hofert\", role = \"ctb\"), person(\"Martin\", \"Modrák\", role = \"ctb\"), person(\"Michael\", \"Chirico\", role = \"ctb\"), person(\"Michael\", \"Friendly\", role = \"ctb\"), person(\"Michal\", \"Bojanowski\", role = \"ctb\"), person(\"Michel\", \"Kuhlmann\", role = \"ctb\"), person(\"Miller\", \"Patrick\", role = \"ctb\"), person(\"Nacho\", \"Caballero\", role = \"ctb\"), person(\"Nick\", \"Salkowski\", role = \"ctb\"), person(\"Niels Richard\", \"Hansen\", role = \"ctb\"), person(\"Noam\", \"Ross\", role = \"ctb\"), person(\"Obada\", \"Mahdi\", role = \"ctb\"), person(\"Pavel N.\", \"Krivitsky\", role = \"ctb\", comment=c(ORCID = \"0000-0002-9101-3362\")), person(\"Pedro\", \"Faria\", role = \"ctb\"), person(\"Qiang\", \"Li\", role = \"ctb\"), person(\"Ramnath\", \"Vaidyanathan\", role = \"ctb\"), person(\"Richard\", \"Cotton\", role = \"ctb\"), person(\"Robert\", \"Krzyzanowski\", role = \"ctb\"), person(\"Rodrigo\", \"Copetti\", role = \"ctb\"), person(\"Romain\", \"Francois\", role = \"ctb\"), person(\"Ruaridh\", \"Williamson\", role = \"ctb\"), person(\"Sagiru\", \"Mati\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1413-3974\")), person(\"Scott\", \"Kostyshak\", role = \"ctb\"), person(\"Sebastian\", \"Meyer\", role = \"ctb\"), person(\"Sietse\", \"Brouwer\", role = \"ctb\"), person(c(\"Simon\", \"de\"), \"Bernard\", role = \"ctb\"), person(\"Sylvain\", \"Rousseau\", role = \"ctb\"), person(\"Taiyun\", \"Wei\", role = \"ctb\"), person(\"Thibaut\", \"Assus\", role = \"ctb\"), person(\"Thibaut\", \"Lamadon\", role = \"ctb\"), person(\"Thomas\", \"Leeper\", role = \"ctb\"), person(\"Tim\", \"Mastny\", role = \"ctb\"), person(\"Tom\", \"Torsney-Weir\", role = \"ctb\"), person(\"Trevor\", \"Davis\", role = \"ctb\"), person(\"Viktoras\", \"Veitas\", role = \"ctb\"), person(\"Weicheng\", \"Zhu\", role = \"ctb\"), person(\"Wush\", \"Wu\", role = \"ctb\"), person(\"Zachary\", \"Foster\", role = \"ctb\"), person(\"Zhian N.\", \"Kamvar\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1458-7108\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a general-purpose tool for dynamic report generation in R using Literate Programming techniques.", "Depends": ["R (>= 3.6.0)"], "Imports": ["evaluate (>= 0.15)", "highr (>= 0.11)", "methods", "tools", "xfun (>= 0.51)", "yaml (>= 2.1.19)"], "Suggests": ["bslib", "codetools", "DBI (>= 0.4-1)", "digest", "formatR", "gifski", "gridSVG", "htmlwidgets (>= 0.7)", "jpeg", "JuliaCall (>= 0.11.1)", "magick", "litedown", "markdown (>= 1.3)", "png", "ragg", "reticulate (>= 1.4)", "rgl (>= 0.95.1201)", "rlang", "rmarkdown", "sass", "showtext", "styler (>= 1.2.0)", "targets (>= 0.6.0)", "testit", "tibble", "tikzDevice (>= 0.10)", "tinytex (>= 0.56)", "webshot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "svglite"], "License": "GPL", "URL": "https://yihui.org/knitr/", "BugReports": "https://github.com/yihui/knitr/issues", "Encoding": "UTF-8", "VignetteBuilder": "litedown, knitr", "SystemRequirements": "Package vignettes based on R Markdown v2 or reStructuredText require Pandoc (http://pandoc.org). The function rst2pdf() requires rst2pdf (https://github.com/rst2pdf/rst2pdf).", "Collate": "'block.R' 'cache.R' 'citation.R' 'hooks-html.R' 'plot.R' 'utils.R' 'defaults.R' 'concordance.R' 'engine.R' 'highlight.R' 'themes.R' 'header.R' 'hooks-asciidoc.R' 'hooks-chunk.R' 'hooks-extra.R' 'hooks-latex.R' 'hooks-md.R' 'hooks-rst.R' 'hooks-textile.R' 'hooks.R' 'output.R' 'package.R' 'pandoc.R' 'params.R' 'parser.R' 'pattern.R' 'rocco.R' 'spin.R' 'table.R' 'template.R' 'utils-conversion.R' 'utils-rd2html.R' 'utils-string.R' 'utils-sweave.R' 'utils-upload.R' 'utils-vignettes.R' 'zzz.R'", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de), <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-8335-495X>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the Sweavel package at inst/mi<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>.s<PERSON>), <PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON><PERSON> [ctb], James Manton [ctb], Jared Lander [ctb], Jason Punyon [ctb], Javier Luraschi [ctb], Jeff Arnold [ctb], Jenny Bryan [ctb], Jeremy Ashkenas [ctb, cph] (the CSS file at inst/misc/docco-classic.css), Jeremy Stephens [ctb], Jim Hester [ctb], Joe Cheng [ctb], Johannes Ranke [ctb], John Honaker [ctb], John Muschelli [ctb], Jonathan Keane [ctb], JJ Allaire [ctb], Johan Toloe [ctb], Jonathan Sidi [ctb], Joseph Larmarange [ctb], Julien Barnier [ctb], Kaiyin Zhong [ctb], Kamil Slowikowski [ctb], Karl Forner [ctb], Kevin K. Smith [ctb], Kirill Mueller [ctb], Kohske Takahashi [ctb], Lorenz Walthert [ctb], Lucas Gallindo [ctb], Marius Hofert [ctb], Martin Modrák [ctb], Michael Chirico [ctb], Michael Friendly [ctb], Michal Bojanowski [ctb], Michel Kuhlmann [ctb], Miller Patrick [ctb], Nacho Caballero [ctb], Nick Salkowski [ctb], Niels Richard Hansen [ctb], Noam Ross [ctb], Obada Mahdi [ctb], Pavel N. Krivitsky [ctb] (<https://orcid.org/0000-0002-9101-3362>), Pedro Faria [ctb], Qiang Li [ctb], Ramnath Vaidyanathan [ctb], Richard Cotton [ctb], Robert Krzyzanowski [ctb], Rodrigo Copetti [ctb], Romain Francois [ctb], Ruaridh Williamson [ctb], Sagiru Mati [ctb] (<https://orcid.org/0000-0003-1413-3974>), Scott Kostyshak [ctb], Sebastian Meyer [ctb], Sietse Brouwer [ctb], Simon de Bernard [ctb], Sylvain Rousseau [ctb], Taiyun Wei [ctb], Thibaut Assus [ctb], Thibaut Lamadon [ctb], Thomas Leeper [ctb], Tim Mastny [ctb], Tom Torsney-Weir [ctb], Trevor Davis [ctb], Viktoras Veitas [ctb], Weicheng Zhu [ctb], Wush Wu [ctb], Zachary Foster [ctb], Zhian N. Kamvar [ctb] (<https://orcid.org/0000-0003-1458-7108>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lambda.r": {"Package": "lambda.r", "Version": "1.2.4", "Source": "Repository", "Type": "Package", "Title": "Modeling Data with Functional Programming", "Date": "2019-09-15", "Depends": ["R (>= 3.0.0)"], "Imports": ["formatR"], "Suggests": ["testit"], "Author": "<PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "A language extension to efficiently write functional programs in R. Syntax extensions include multi-part function definitions, pattern matching, guard statements, built-in (optional) type safety.", "License": "LGPL-3", "LazyLoad": "yes", "NeedsCompilation": "no", "Repository": "CRAN"}, "lifecycle": {"Package": "lifecycle", "Version": "1.0.4", "Source": "Repository", "Title": "Manage the Life Cycle of your Package Functions", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Manage the life cycle of your exported functions with shared conventions, documentation badges, and user-friendly deprecation warnings.", "License": "MIT + file LICENSE", "URL": "https://lifecycle.r-lib.org/, https://github.com/r-lib/lifecycle", "BugReports": "https://github.com/r-lib/lifecycle/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.0)", "glue", "rlang (>= 1.1.0)"], "Suggests": ["covr", "crayon", "knitr", "lintr", "rmarkdown", "testthat (>= 3.0.1)", "tibble", "tidyverse", "tools", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate, usethis", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "magrittr": {"Package": "magrit<PERSON>", "Version": "2.0.4", "Source": "Repository", "Type": "Package", "Title": "A Forward-Pipe Operator for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cph\"), comment = \"Original author and creator of magrittr\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"cre\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides a mechanism for chaining commands with a new forward-pipe operator, %>%. This operator will forward a value, or the result of an expression, into the next function call/expression. There is flexible support for the type of right-hand side expressions. For more information, see package vignette.  To quote <PERSON>, \"Ceci n'est pas un pipe.\"", "License": "MIT + file LICENSE", "URL": "https://magrittr.tidyverse.org, https://github.com/tidyverse/magrittr", "BugReports": "https://github.com/tidyverse/magrittr/issues", "Depends": ["R (>= 3.4.0)"], "Suggests": ["covr", "knitr", "rlang", "rmarkdown", "testthat"], "VignetteBuilder": "knitr", "ByteCompile": "Yes", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph] (Original author and creator of magrittr), <PERSON> [aut], <PERSON> [cre], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "maps": {"Package": "maps", "Version": "3.4.3", "Source": "Repository", "Title": "Draw Geographical Maps", "Date": "2025-05-15", "Description": "Display of maps.  Projection code and larger maps are in separate packages ('mapproj' and 'mapdata').", "Depends": ["R (>= 3.5.0)"], "Imports": ["graphics", "utils"], "LazyData": "yes", "Suggests": ["mapproj (>= 1.2-0)", "mapdata (>= 2.3.0)", "sf", "rnaturalearth"], "License": "GPL-2", "URL": "https://github.com/adeckmyn/maps", "BugReports": "https://github.com/adeckmyn/maps/issues", "Authors@R": "c(person(given  = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role   = \"aut\", comment= \"Original S code\"), person(given  = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"Wilks\", role   = \"aut\", comment= \"Original S code\"), person(given  = \"<PERSON>\", family = \"<PERSON>rigg\", role   = c(\"trl\", \"aut\"), comment= \"R version\"), person(given  = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"Minka\", role   = \"aut\", comment= \"Enhancements\"), person(given  = \"<PERSON>\", family = \"<PERSON>my<PERSON>\", role   = c(\"aut\", \"cre\"), comment= c(ORCID=\"0009-0002-2010-8310\"), email  = \"<EMAIL>\"))", "NeedsCompilation": "yes", "Repository": "CRAN", "Author": "<PERSON> [aut] (Original S code), <PERSON> [aut] (Original S code), <PERSON> [trl, aut] (R version), <PERSON> [aut] (Enhancements), <PERSON> [aut, cre] (ORCID: <https://orcid.org/0009-0002-2010-8310>)", "Maintainer": "<PERSON> <<EMAIL>>"}, "miniUI": {"Package": "miniUI", "Version": "0.1.2", "Source": "Repository", "Type": "Package", "Title": "Shiny UI Widgets for Small Screens", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = c(\"cre\", \"aut\"), email = \"<EMAIL>\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides UI widget and layout functions for writing Shiny apps that work well on small screens.", "License": "GPL-3", "URL": "https://github.com/rstudio/miniUI", "BugReports": "https://github.com/rstudio/miniUI/issues", "Imports": ["shiny (>= 0.13)", "htmltools (>= 0.3)", "utils"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [cre, aut], Posit Software, PBC [cph, fnd] (03wc8by49)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "misc3d": {"Package": "misc3d", "Version": "0.9-1", "Source": "Repository", "Title": "Miscellaneous 3D Plots", "Author": "<PERSON> and <PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Suggests": ["rgl", "tkrplot", "MASS"], "Imports": ["grDevices", "graphics", "stats", "tcltk"], "Description": "A collection of miscellaneous 3d plots, including isosurfaces.", "URL": "https://gitlab.com/luke-tierney/misc3d", "License": "GPL", "NeedsCompilation": "no", "Repository": "CRAN"}, "openssl": {"Package": "openssl", "Version": "2.3.4", "Source": "Repository", "Type": "Package", "Title": "Toolkit for Encryption, Signatures and Certificates Based on OpenSSL", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"))", "Description": "Bindings to OpenSSL libssl and libcrypto, plus custom SSH key parsers. Supports RSA, DSA and EC curves P-256, P-384, P-521, and curve25519. Cryptographic signatures can either be created and verified manually or via x509 certificates.  AES can be used in cbc, ctr or gcm mode for symmetric encryption; RSA for asymmetric (public key) encryption or EC for <PERSON><PERSON><PERSON>. High-level envelope functions  combine RSA and AES for encrypting arbitrary sized data. Other utilities include key generators, hash functions (md5, sha1, sha256, etc), base64 encoder, a secure random number generator, and 'bignum' math methods for manually performing crypto  calculations on large multibyte integers.", "License": "MIT + file LICENSE", "URL": "https://jeroen.r-universe.dev/openssl", "BugReports": "https://github.com/jeroen/openssl/issues", "SystemRequirements": "OpenSSL >= 1.0.2", "VignetteBuilder": "knitr", "Imports": ["askpass"], "Suggests": ["curl", "testthat (>= 2.1.0)", "digest", "knitr", "rmarkdown", "jsonlite", "jose", "sodium"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ordinal": {"Package": "ordinal", "Version": "2023.12-4.1", "Source": "Repository", "Type": "Package", "Title": "Regression Models for Ordinal Data", "Date": "2023-12-04", "Authors@R": "person(given=\"<PERSON><PERSON><PERSON>\", family=\"<PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\"))", "LazyData": "true", "ByteCompile": "yes", "Depends": ["R (>= 2.13.0)", "stats", "methods"], "Imports": ["ucminf", "MASS", "Matrix", "numDeriv", "nlme"], "Suggests": ["lme4", "nnet", "xtable", "testthat (>= 0.8)", "tools"], "Description": "Implementation of cumulative link (mixed) models also known as ordered regression models, proportional odds models, proportional hazards models for grouped survival times and ordered logit/probit/... models. Estimation is via maximum likelihood and mixed models are fitted with the Laplace approximation and adaptive Gauss-Hermite quadrature. Multiple random effect terms are allowed and they may be nested, crossed or partially nested/crossed. Restrictions of symmetry and equidistance can be imposed on the thresholds (cut-points/intercepts). Standard model methods are available (summary, anova, drop-methods, step, confint, predict etc.) in addition to profile methods and slice methods for visualizing the likelihood function and checking convergence.", "License": "GPL (>= 2)", "NeedsCompilation": "yes", "URL": "https://github.com/runehaubo/ordinal", "BugReports": "https://github.com/runehaubo/ordinal/issues", "Author": "<PERSON><PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON>e <PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pillar": {"Package": "pillar", "Version": "1.11.1", "Source": "Repository", "Title": "Coloured Formatting for Columns", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\"), person(given = \"RStudio\", role = \"cph\"))", "Description": "Provides 'pillar' and 'colonnade' generics designed for formatting columns of data using the full range of colours provided by modern terminals.", "License": "MIT + file LICENSE", "URL": "https://pillar.r-lib.org/, https://github.com/r-lib/pillar", "BugReports": "https://github.com/r-lib/pillar/issues", "Imports": ["cli (>= 2.3.0)", "glue", "lifecycle", "rlang (>= 1.0.2)", "utf8 (>= 1.1.0)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bit64", "DBI", "debugme", "DiagrammeR", "dplyr", "formattable", "ggplot2", "knitr", "lubridate", "nanotime", "nycflights13", "palmerpenguins", "rmarkdown", "scales", "stringi", "survival", "testthat (>= 3.1.1)", "tibble", "units (>= 0.7.2)", "vdiffr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.3.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "format_multi_fuzz, format_multi_fuzz_2, format_multi, ctl_colonnade, ctl_colonnade_1, ctl_colonnade_2", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/gha/extra-packages": "units=?ignore-before-r=4.3.0", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON>tudio [cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgbuild": {"Package": "pkgbuild", "Version": "1.4.8", "Source": "Repository", "Title": "Find Tools Needed to Build R Packages", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides functions used to build R packages. Locates compilers needed to build R packages on various platforms and ensures the PATH is configured appropriately so R can use them.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/pkgbuild, https://pkgbuild.r-lib.org", "BugReports": "https://github.com/r-lib/pkgbuild/issues", "Depends": ["R (>= 3.5)"], "Imports": ["callr (>= 3.2.0)", "cli (>= 3.4.0)", "desc", "processx", "R6"], "Suggests": ["covr", "cpp11", "knitr", "Rcpp", "rmarkdown", "testthat (>= 3.2.0)", "withr (>= 2.3.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-04-30", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgdown": {"Package": "pkgdown", "Version": "2.1.3", "Source": "Repository", "Title": "Make Static HTML Documentation for a Package", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-6299-179X\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2815-0399\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Generate an attractive and useful website from a source package.  'pkgdown' converts your documentation, vignettes, 'README', and more to 'HTML' making it easy to share information about your package online.", "License": "MIT + file LICENSE", "URL": "https://pkgdown.r-lib.org/, https://github.com/r-lib/pkgdown", "BugReports": "https://github.com/r-lib/pkgdown/issues", "Depends": ["R (>= 4.0.0)"], "Imports": ["bslib (>= 0.5.1)", "callr (>= 3.7.3)", "cli (>= 3.6.1)", "desc (>= 1.4.0)", "downlit (>= 0.4.4)", "fontawesome", "fs (>= 1.4.0)", "httr2 (>= 1.0.2)", "jsonlite", "openssl", "purrr (>= 1.0.0)", "ragg (>= 1.4.0)", "rlang (>= 1.1.4)", "rmarkdown (>= 2.27)", "tibble", "whisker", "withr (>= 2.4.3)", "xml2 (>= 1.3.1)", "yaml"], "Suggests": ["covr", "diffviewer", "evaluate (>= 0.24.0)", "gert", "gt", "htmltools", "htmlwidgets", "knitr (>= 1.50)", "lifecycle", "magick", "methods", "pkgload (>= 1.0.2)", "quarto", "rsconnect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rticles", "sass", "testthat (>= 3.1.3)", "tools"], "VignetteBuilder": "knitr, quarto", "Config/Needs/website": "usethis, servr", "Config/potools/style": "explicit", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "build-article, build-quarto-article, build-reference", "Encoding": "UTF-8", "SystemRequirements": "pandoc", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-6299-179X>), <PERSON><PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-2815-0399>), <PERSON> [aut], <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-5329-5987>), Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgload": {"Package": "pkgload", "Version": "1.4.1", "Source": "Repository", "Title": "Simulate Package Installation and Attach", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"R Core team\", role = \"ctb\", comment = \"Some namespace and vignette code extracted from base R\") )", "Description": "Simulates the process of installing a package and then attaching it. This is a key part of the 'devtools' package as it allows you to rapidly iterate while developing a package.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/pkgload, https://pkgload.r-lib.org", "BugReports": "https://github.com/r-lib/pkgload/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli (>= 3.3.0)", "desc", "fs", "glue", "lifecycle", "methods", "pkgbuild", "processx", "rlang (>= 1.1.1)", "rprojroot", "utils"], "Suggests": ["bitops", "jsonlite", "mathjaxr", "pak", "Rcpp", "remotes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= *******)", "usethis", "withr"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Config/testthat/start-first": "dll", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], Posit Software, PBC [cph, fnd], R Core team [ctb] (Some namespace and vignette code extracted from base R)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "plogr": {"Package": "plogr", "Version": "0.2.0", "Source": "Repository", "Title": "The 'plog' C++ Logging Library", "Date": "2018-03-24", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"Author of the bundled plog library\"))", "Description": "A simple header-only logging library for C++. Add 'LinkingTo: plogr' to 'DESCRIPTION', and '#include <plogr.h>' in your C++ modules to use it.", "Suggests": ["Rcpp"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "LazyData": "true", "URL": "https://github.com/krlmlr/plogr#readme", "BugReports": "https://github.com/krlmlr/plogr/issues", "RoxygenNote": "6.0.1.9000", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON> [cph] (Author of the bundled plog library)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "plot3D": {"Package": "plot3D", "Version": "1.4.2", "Source": "Repository", "Title": "Plotting Multi-Dimensional Data", "Authors@R": "person(given = \"<PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4603-7100\"))", "Depends": ["R (>= 2.15)"], "Imports": ["misc3d", "stats", "graphics", "grDevices", "methods"], "Description": "Functions for viewing 2-D and 3-D data, including perspective plots, slice plots, surface plots, scatter plots, etc. Includes data sets from oceanography.", "License": "GPL (>= 3.0)", "LazyData": "yes", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4603-7100>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "plyr": {"Package": "plyr", "Version": "1.8.9", "Source": "Repository", "Title": "Tools for Splitting, Applying and Combining Data", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"))", "Description": "A set of tools that solves a common set of problems: you need to break a big problem down into manageable pieces, operate on each piece and then put all the pieces back together.  For example, you might want to fit a model to each spatial location or time point in your study, summarise data by panels or collapse high-dimensional arrays to simpler summary statistics. The development of 'plyr' has been generously supported by 'Becton Dickinson'.", "License": "MIT + file LICENSE", "URL": "http://had.co.nz/plyr, https://github.com/hadley/plyr", "BugReports": "https://github.com/hadley/plyr/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["Rcpp (>= 0.11.0)"], "Suggests": ["abind", "covr", "doP<PERSON>llel", "foreach", "iterators", "itertools", "tcltk", "testthat"], "LinkingTo": ["Rcpp"], "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "praise": {"Package": "praise", "Version": "1.0.0", "Source": "Repository", "Title": "Praise Users", "Author": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Build friendly R packages that praise their users if they have done something good, or they just need it to feel better.", "License": "MIT + file LICENSE", "LazyData": "true", "URL": "https://github.com/gaborcsardi/praise", "BugReports": "https://github.com/gaborcsardi/praise/issues", "Suggests": ["testthat"], "Collate": "'adjective.R' 'adverb.R' 'exclamation.R' 'verb.R' 'rpackage.R' 'package.R'", "NeedsCompilation": "no", "Repository": "CRAN"}, "processx": {"Package": "processx", "Version": "3.8.6", "Source": "Repository", "Title": "Execute and Control System Processes", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\"), comment = c(ORCID = \"0000-0001-7098-9676\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"Ascent Digital Services\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to run system processes in the background.  It can check if a background process is running; wait on a background process to finish; get the exit status of finished processes; kill background processes. It can read the standard output and error of the processes, using non-blocking connections. 'processx' can poll a process for standard output or error, with a timeout. It can also poll several processes at once.", "License": "MIT + file LICENSE", "URL": "https://processx.r-lib.org, https://github.com/r-lib/processx", "BugReports": "https://github.com/r-lib/processx/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["ps (>= 1.2.0)", "R6", "utils"], "Suggests": ["callr (>= 3.7.3)", "cli (>= 3.3.0)", "codetools", "covr", "curl", "debugme", "parallel", "rlang (>= 1.0.2)", "testthat (>= 3.0.0)", "webfakes", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.1.9000", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0001-7098-9676>), <PERSON> [aut], Posit Software, PBC [cph, fnd], Ascent Digital Services [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "profvis": {"Package": "profvis", "Version": "0.4.0", "Source": "Repository", "Title": "Interactive Visualizations for Profiling R Code", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library\"), person(, \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/htmlwidgets/lib/jquery/AUTHORS.txt\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"D3 library\"), person(, \"D3 contributors\", role = \"ctb\", comment = \"D3 library\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"highlight.js library\") )", "Description": "Interactive visualizations for profiling R code.", "License": "MIT + file LICENSE", "URL": "https://profvis.r-lib.org, https://github.com/r-lib/profvis", "BugReports": "https://github.com/r-lib/profvis/issues", "Depends": ["R (>= 4.0)"], "Imports": ["htmlwidgets (>= 0.3.2)", "rlang (>= 1.1.0)", "vctrs"], "Suggests": ["htmltools", "knitr", "rmarkdown", "shiny", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate, rmarkdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd], jQuery Foundation [cph] (jQuery library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/htmlwidgets/lib/jquery/AUTHORS.txt), <PERSON> [ctb, cph] (D3 library), D3 contributors [ctb] (D3 library), <PERSON> [ctb, cph] (highlight.js library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "proxy": {"Package": "proxy", "Version": "0.4-27", "Source": "Repository", "Type": "Package", "Title": "Distance and Similarity Measures", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<PERSON><PERSON>@R-project.org\"), person(given = \"Christian\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"))", "Description": "Provides an extensible framework for the efficient calculation of auto- and cross-proximities, along with implementations of the most popular ones.", "Depends": ["R (>= 3.4.0)"], "Imports": ["stats", "utils"], "Suggests": ["cba"], "Collate": "registry.R database.R dist.R similarities.R dissimilarities.R util.R seal.R", "License": "GPL-2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut]", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "CRAN"}, "ps": {"Package": "ps", "Version": "1.9.1", "Source": "Repository", "Title": "List, Query, Manipulate System Processes", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>'\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "List, query and manipulate all system processes, on 'Windows', 'Linux' and 'macOS'.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/ps, https://ps.r-lib.org/", "BugReports": "https://github.com/r-lib/ps/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "curl", "pillar", "pingr", "processx (>= 3.1.0)", "R6", "rlang", "testthat (>= 3.0.0)", "webfakes", "withr"], "Biarch": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pscl": {"Package": "pscl", "Version": "1.5.9", "Source": "Repository", "Date": "2024-01-14", "Title": "Political Science Computational Laboratory", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-7421-4034\") ), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\"), comment = c(ORCID = \"0000-0001-7895-9420\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\"), comment = c(ORCID = \"0000-0003-0918-3766\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\"), comment = c(ORCID = \"0000-0002-0402-6297\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\"), comment = c(ORCID = \"0000-0001-8045-6531\")) )", "Imports": ["MASS", "datasets", "grDevices", "graphics", "stats", "utils"], "Suggests": ["lattice", "MCMCpack", "car", "lmtest", "sandwich", "zoo", "coda", "vcd", "mvtnorm", "mgcv"], "Description": "Bayesian analysis of item-response theory (IRT) models, roll call analysis; computing highest density regions;  maximum likelihood estimation of zero-inflated and hurdle models for count data; goodness-of-fit measures for GLMs; data sets used in writing\tand teaching; seats-votes curves.", "LazyData": "true", "License": "GPL-2", "URL": "https://github.com/atahk/pscl", "RoxygenNote": "7.3.0", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-7421-4034>), <PERSON> [ctb] (<https://orcid.org/0000-0001-7895-9420>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-0918-3766>), <PERSON> [ctb] (<https://orcid.org/0000-0002-0402-6297>), <PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0001-8045-6531>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "purrr": {"Package": "purrr", "Version": "1.1.0", "Source": "Repository", "Title": "Functional Programming Tools", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"https://ror.org/03wc8by49\")) )", "Description": "A complete and consistent functional programming toolkit for R.", "License": "MIT + file LICENSE", "URL": "https://purrr.tidyverse.org/, https://github.com/tidyverse/purrr", "BugReports": "https://github.com/tidyverse/purrr/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli (>= 3.6.1)", "lifecycle (>= 1.0.3)", "magrittr (>= 1.5.0)", "rlang (>= 1.1.1)", "vctrs (>= 0.6.3)"], "Suggests": ["carrier (>= 0.2.0)", "covr", "dplyr (>= 0.7.8)", "httr", "knitr", "lubridate", "mirai (>= 2.4.0)", "rmarkdown", "testthat (>= 3.0.0)", "tibble", "tidyselect"], "LinkingTo": ["cli"], "VignetteBuilder": "knitr", "Biarch": "true", "Config/build/compilation-database": "true", "Config/Needs/website": "tidyverse/tidytemplate, tidyr", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rcmdcheck": {"Package": "rcmdcheck", "Version": "1.4.0", "Source": "Repository", "Title": "Run 'R CMD check' from 'R' and Capture Results", "Authors@R": "person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"cre\", \"aut\"), email = \"<EMAIL>\")", "Description": "Run 'R CMD check' from 'R' and capture the results of the individual checks. Supports running checks in the background, timeouts, pretty printing and comparing check results.", "License": "MIT + file LICENSE", "URL": "https://r-lib.github.io/rcmdcheck/, https://github.com/r-Lib/rcmdcheck#readme", "BugReports": "https://github.com/r-Lib/rcmdcheck/issues", "Imports": ["callr (>= 3.1.1.9000)", "cli (>= 3.0.0)", "curl", "desc (>= 1.2.0)", "digest", "pkgbuild", "prettyunits", "R6", "rprojroot", "sessioninfo (>= 1.1.1)", "utils", "withr", "xopen"], "Suggests": ["covr", "knitr", "mockery", "processx", "ps", "rmarkdown", "svglite", "testthat", "webfakes"], "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [cre, aut]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "renv": {"Package": "renv", "Version": "1.1.5", "Source": "Repository", "Type": "Package", "Title": "Project Environments", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A dependency management toolkit for R. Using 'renv', you can create and manage project-local R libraries, save the state of these libraries to a 'lockfile', and later restore your library as required. Together, these tools can help make your projects more isolated, portable, and reproducible.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/renv/, https://github.com/rstudio/renv", "BugReports": "https://github.com/rstudio/renv/issues", "Imports": ["utils"], "Suggests": ["BiocManager", "cli", "compiler", "covr", "cpp11", "devtools", "generics", "gitcreds", "jsonlite", "jsonvalidate", "knitr", "miniUI", "modules", "packrat", "pak", "R6", "remotes", "reticulate", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiny", "testthat", "uuid", "waldo", "yaml", "webfakes"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "bioconductor,python,install,restore,snapshot,retrieve,remotes", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-2880-7407>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "reshape": {"Package": "reshape", "Version": "0.8.10", "Source": "Repository", "Title": "Flexibly Reshape Data", "Description": "Flexibly restructure and aggregate data using  just two functions: melt and cast.", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\"))", "URL": "http://had.co.nz/reshape", "Depends": ["R (>= 2.6.1)"], "Imports": ["plyr"], "License": "MIT + file LICENSE", "LazyData": "true", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "reshape2": {"Package": "reshape2", "Version": "1.4.4", "Source": "Repository", "Title": "Flexibly Reshape Data: A Reboot of the Reshape Package", "Author": "<PERSON> <<EMAIL>>", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Flexibly restructure and aggregate data using just two functions: melt and 'dcast' (or 'acast').", "License": "MIT + file LICENSE", "URL": "https://github.com/hadley/reshape", "BugReports": "https://github.com/hadley/reshape/issues", "Depends": ["R (>= 3.1)"], "Imports": ["plyr (>= 1.8.1)", "Rcpp", "stringr"], "Suggests": ["covr", "lattice", "testthat (>= 0.8.0)"], "LinkingTo": ["Rcpp"], "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.1.0", "NeedsCompilation": "yes", "Repository": "CRAN"}, "rhdf5": {"Package": "rhdf5", "Version": "2.46.1", "Source": "Bioconductor", "Type": "Package", "Title": "R Interface to HDF5", "Authors@R": "c(person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\")),  person(\"<PERSON>\", \"<PERSON>\",  role=c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-7800-3848\") ), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"))", "Description": "This package provides an interface between HDF5 and R.  HDF5's main features are the ability to store and access very large and/or complex datasets and a wide variety of metadata on mass storage (disk)  through a completely portable file format. The rhdf5 package is thus suited for the exchange of large and/or complex datasets between R and other  software package, and for letting R applications work on datasets that are  larger than the available RAM.", "License": "Artistic-2.0", "URL": "https://github.com/grimbough/rhdf5", "BugReports": "https://github.com/grimbough/rhdf5/issues", "LazyLoad": "true", "VignetteBuilder": "knitr", "Imports": ["Rhdf5lib (>= 1.13.4)", "rhdf5filters", "S4Vectors"], "Depends": ["R (>= 4.0.0)", "methods"], "Suggests": ["bit64", "BiocStyle", "knitr", "rmarkdown", "testthat", "bench", "dplyr", "ggplot2", "mockery", "BiocParallel"], "LinkingTo": ["Rhdf5lib"], "SystemRequirements": "GNU make", "biocViews": "Infrastructure, DataImport", "Encoding": "UTF-8", "Roxygen": "list(markdown = TRUE)", "RoxygenNote": "7.2.3", "git_url": "https://git.bioconductor.org/packages/rhdf5", "git_branch": "RELEASE_3_18", "git_last_commit": "27bab89", "git_last_commit_date": "2023-11-29", "Repository": "Bioconductor 3.18", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-7800-3848>), <PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>"}, "rhdf5filters": {"Package": "rhdf5filters", "Version": "1.14.1", "Source": "Bioconductor", "Type": "Package", "Title": "HDF5 Compression Filters", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\",  role=c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-7800-3848\")) )", "Description": "Provides a collection of additional compression filters for HDF5  datasets. The package is intended to provide seemless integration with  rhdf5, however the compiled filters can also be used with external  applications.", "License": "BSD_2_clause + file LICENSE", "LazyLoad": "true", "VignetteBuilder": "knitr", "Suggests": ["BiocStyle", "knitr", "rmarkdown", "tinytest", "rhdf5 (>= 2.34.0)"], "SystemRequirements": "GNU make", "URL": "https://github.com/grimbough/rhdf5filters", "BugReports": "https://github.com/grimbough/rhdf5filters", "LinkingTo": ["Rhdf5lib"], "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "biocViews": "Infrastructure, DataImport", "git_url": "https://git.bioconductor.org/packages/rhdf5filters", "git_branch": "RELEASE_3_18", "git_last_commit": "5ddd484", "git_last_commit_date": "2023-11-06", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-7800-3848>)", "Maintainer": "<PERSON> <<EMAIL>>"}, "rlang": {"Package": "rlang", "Version": "1.1.6", "Source": "Repository", "Title": "Functions for Base Types and Core R and 'Tidyverse' Features", "Description": "A toolbox for working with base types, core R features like the condition system, and core 'Tidyverse' features like tidy evaluation.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", \"aut\"), person(given = \"mikefc\", email = \"<EMAIL>\", role = \"cph\", comment = \"Hash implementation based on <PERSON>'s xxhashlite\"), person(given = \"Yann\", family = \"Collet\", role = \"cph\", comment = \"Author of the embedded xxHash library\"), person(given = \"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "ByteCompile": "true", "Biarch": "true", "Depends": ["R (>= 3.5.0)"], "Imports": ["utils"], "Suggests": ["cli (>= 3.1.0)", "covr", "crayon", "desc", "fs", "glue", "knitr", "magrit<PERSON>", "methods", "pillar", "pkgload", "rmarkdown", "stats", "testthat (>= 3.2.0)", "tibble", "usethis", "vctrs (>= 0.2.3)", "withr"], "Enhances": ["winch"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "URL": "https://rlang.r-lib.org, https://github.com/r-lib/rlang", "BugReports": "https://github.com/r-lib/rlang/issues", "Config/build/compilation-database": "true", "Config/testthat/edition": "3", "Config/Needs/website": "dplyr, tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], mikef<PERSON> [cph] (Hash implementation based on <PERSON>'s xxhashlite), <PERSON><PERSON> [cph] (Author of the embedded xxHash library), Posit, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rprojroot": {"Package": "rprojroot", "Version": "2.1.1", "Source": "Repository", "Title": "Finding Files in Project Subdirectories", "Authors@R": "person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\"))", "Description": "Robust, reliable and flexible paths to files below a project root. The 'root' of a project is defined as a directory that matches a certain criterion, e.g., it contains a certain regular file.", "License": "MIT + file LICENSE", "URL": "https://rprojroot.r-lib.org/, https://github.com/r-lib/rprojroot", "BugReports": "https://github.com/r-lib/rprojroot/issues", "Depends": ["R (>= 3.0.0)"], "Suggests": ["covr", "knitr", "lifecycle", "rlang", "rmarkdown", "testthat (>= 3.2.0)", "withr"], "VignetteBuilder": "knitr", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rversions": {"Package": "rversions", "Version": "2.1.2", "Source": "Repository", "Title": "Query 'R' Versions, Including 'r-release' and 'r-oldrel'", "Authors@R": "c(person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"Ooms\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"R Consortium\", role = \"fnd\"))", "Description": "Query the main 'R' 'SVN' repository to find the versions 'r-release' and 'r-oldrel' refer to, and also all previous 'R' versions and their release dates.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-hub/rversions, https://r-hub.github.io/rversions/", "BugReports": "https://github.com/r-hub/rversions/issues", "Imports": ["curl", "utils", "xml2 (>= 1.0.0)"], "Suggests": ["covr", "mockery", "testthat"], "Encoding": "UTF-8", "RoxygenNote": "7.2.1.9000", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON> [fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rvest": {"Package": "rvest", "Version": "1.0.4", "Source": "Repository", "Title": "<PERSON><PERSON> (Scrape) Web Pages", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Wrappers around the 'xml2' and 'httr' packages to make it easy to download, then manipulate, HTML and XML.", "License": "MIT + file LICENSE", "URL": "https://rvest.tidyverse.org/, https://github.com/tidyverse/rvest", "BugReports": "https://github.com/tidyverse/rvest/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli", "glue", "httr (>= 0.5)", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "rlang (>= 1.1.0)", "selectr", "tibble", "xml2 (>= 1.3)"], "Suggests": ["chromote", "covr", "knitr", "R6", "readr", "repurrrsive", "rmarkdown", "spelling", "stringi (>= 0.3.1)", "testthat (>= 3.0.2)", "webfakes"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "s2": {"Package": "s2", "Version": "1.1.7", "Source": "Repository", "Title": "Spherical Geometry Operators Using the S2 Geometry Library", "Authors@R": "c( person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-9415-4582\")), person(given = \"Edzer\", family = \"Peb<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8049-7069\")), person(\"Ege\", \"<PERSON>uba<PERSON>\", email=\"<EMAIL>\", role = c(\"aut\")), person(\"<PERSON><PERSON><PERSON>\", \"Ooms\", , \"<EMAIL>\", role = \"ctb\", comment = \"configure script\"), person(family = \"Google, Inc.\", role = \"cph\", comment = \"Original s2geometry.io source code\") )", "Description": "Provides R bindings for Google's s2 library for geometric calculations on the sphere. High-performance constructors and exporters provide high compatibility with existing spatial packages, transformers construct new geometries from existing geometries, predicates provide a means to select geometries based on spatial  relationships, and accessors extract information about geometries.", "License": "Apache License (== 2.0)", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "SystemRequirements": "OpenSSL >= 1.0.1", "LinkingTo": ["Rcpp", "wk"], "Imports": ["Rcpp", "wk (>= 0.6.0)"], "Suggests": ["bit64", "testthat (>= 3.0.0)", "vctrs"], "URL": "https://r-spatial.github.io/s2/, https://github.com/r-spatial/s2, http://s2geometry.io/", "BugReports": "https://github.com/r-spatial/s2/issues", "Depends": ["R (>= 3.0.0)"], "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-9415-4582>), <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0001-8049-7069>), <PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [ctb] (configure script), Google, Inc. [cph] (Original s2geometry.io source code)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "sessioninfo": {"Package": "sessioninfo", "Version": "1.2.3", "Source": "Repository", "Title": "R Session Information", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"cre\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"R Core team\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Query and print information about the current R session.  It is similar to 'utils::sessionInfo()', but includes more information about packages, and where they were installed from.", "License": "GPL-2", "URL": "https://github.com/r-lib/sessioninfo#readme, https://sessioninfo.r-lib.org", "BugReports": "https://github.com/r-lib/sessioninfo/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli (>= 3.1.0)", "tools", "utils"], "Suggests": ["callr", "covr", "gh", "reticulate", "rmarkdown", "testthat (>= 3.2.0)", "withr"], "Config/Needs/website": "pkgdown, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], R Core team [ctb], Posit Software, PBC [cph, fnd]", "Repository": "CRAN"}, "sf": {"Package": "sf", "Version": "1.0-20", "Source": "Repository", "Title": "Simple Features for R", "Authors@R": "c(person(given = \"Ed<PERSON>\", family = \"Peb<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8049-7069\")), person(given = \"<PERSON>\", family = \"Bivand\", role = \"ctb\", comment = c(ORCID = \"0000-0003-2392-6140\")), person(given = \"Etienne\", family = \"Ra<PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"Keitt\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON>ick<PERSON>\", role = \"ctb\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"Ooms\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(given = \"Ki<PERSON>\", family = \"M\\u00fcller\", role = \"ctb\"), person(given = \"<PERSON> Lin\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"ctb\"), person(given = \"Dewey\", family = \"<PERSON>ington\", role = \"ctb\", comment = c(ORCID = \"0000-0002-9415-4582\")) )", "Description": "Support for simple feature access, a standardized way to encode and analyze spatial vector data. Binds to 'GDAL'  <doi:10.5281/zenodo.5884351> for reading and writing data, to 'GEOS' <doi:10.5281/zenodo.11396894> for geometrical operations, and to 'PROJ' <doi:10.5281/zenodo.5884394> for projection conversions and datum transformations. Uses by default the 's2' package for geometry operations on geodetic (long/lat degree) coordinates.", "License": "GPL-2 | MIT + file LICENSE", "URL": "https://r-spatial.github.io/sf/, https://github.com/r-spatial/sf", "BugReports": "https://github.com/r-spatial/sf/issues", "Depends": ["methods", "R (>= 3.3.0)"], "Imports": ["classInt (>= 0.4-1)", "DBI (>= 0.8)", "graphics", "grDevices", "grid", "magrit<PERSON>", "s2 (>= 1.1.0)", "stats", "tools", "units (>= 0.7-0)", "utils"], "Suggests": ["blob", "nanoarrow", "covr", "dplyr (>= 1.0.0)", "ggplot2", "knitr", "lwgeom (>= 0.2-14)", "maps", "mapview", "Matrix", "microbenchmark", "odbc", "pbapply", "pillar", "pool", "raster", "rlang", "rmarkdown", "RPostgres (>= 1.1.0)", "RPostgreSQL", "RSQLite", "sp (>= 1.2-4)", "spatstat (>= 2.0-1)", "spatstat.geom", "spatstat.random", "spatstat.linnet", "spatstat.utils", "stars (>= 0.6-0)", "terra", "testthat (>= 3.0.0)", "tibble (>= 1.4.1)", "tidyr (>= 1.2.0)", "tidyselect (>= 1.0.0)", "tmap (>= 2.0)", "vctrs", "wk (>= 0.9.0)"], "LinkingTo": ["Rcpp"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Config/testthat/edition": "2", "Config/needs/coverage": "XML", "SystemRequirements": "GDAL (>= 2.0.1), GEOS (>= 3.4.0), PROJ (>= 4.8.0), sqlite3", "Collate": "'RcppExports.R' 'init.R' 'import-standalone-s3-register.R' 'crs.R' 'bbox.R' 'read.R' 'db.R' 'sfc.R' 'sfg.R' 'sf.R' 'bind.R' 'wkb.R' 'wkt.R' 'plot.R' 'geom-measures.R' 'geom-predicates.R' 'geom-transformers.R' 'transform.R' 'proj.R' 'sp.R' 'grid.R' 'arith.R' 'tidyverse.R' 'tidyverse-vctrs.R' 'cast_sfg.R' 'cast_sfc.R' 'graticule.R' 'datasets.R' 'aggregate.R' 'agr.R' 'maps.R' 'join.R' 'sample.R' 'valid.R' 'collection_extract.R' 'jitter.R' 'sgbp.R' 'spatstat.R' 'stars.R' 'crop.R' 'gdal_utils.R' 'nearest.R' 'normalize.R' 'sf-package.R' 'defunct.R' 'z_range.R' 'm_range.R' 'shift_longitude.R' 'make_grid.R' 's2.R' 'terra.R' 'geos-overlayng.R' 'break_antimeridian.R'", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0001-8049-7069>), <PERSON> [ctb] (<https://orcid.org/0000-0003-2392-6140>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-4035-0289>), <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-9415-4582>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "spam": {"Package": "spam", "Version": "2.11-1", "Source": "Repository", "Type": "Package", "Title": "SPArse Matrix", "Date": "2025-01-20", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"Furrer\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-6319-2332\")), person(\"<PERSON><PERSON><PERSON>\", \"Gerber\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8545-5263\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-0349-8698\")), person(\"<PERSON>\", \"Gerber\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"Saad\", role = \"ctb\", comment = \"SPARSEKIT http://www-users.cs.umn.edu/~saad/software/SPARSKIT/\"), person(c(\"Esmond\", \"G.\"), \"Ng\", role = \"ctb\", comment = \"<PERSON>ran Cholesky routines\"), person(c(\"<PERSON>\", \"W.\"), \"<PERSON>\", role = \"ctb\", comment = \"<PERSON>ran <PERSON>lesky routines\"), person(c(\"<PERSON>\", \"W.H.\"), \"<PERSON>\", role = \"ctb\", comment = \"<PERSON>ran Cholesky routines\"), person(c(\"Alan\", \"D.\"), \"George\", role = \"ctb\", comment = \"Fortran Cholesky routines\"), person(c(\"Lehoucq\", \"B.\"), \"Rich\", role = \"ctb\", comment = \"ARPACK\"), person(c(\"Maschhoff\"), \"Kristi\", role = \"ctb\", comment = \"ARPACK\"), person(c(\"Sorensen\", \"C.\"), \"Danny\", role = \"ctb\", comment = \"ARPACK\"), person(c(\"Yang\"), \"Chao\", role = \"ctb\", comment = \"ARPACK\"))", "Depends": ["R (>= 4.0)"], "Imports": ["dotCall64", "grid", "methods", "Rcpp (>= *******)"], "LinkingTo": ["Rcpp"], "Suggests": ["spam64", "fields", "Matrix", "testthat", "R.rsp", "truncdist", "knitr", "rmarkdown"], "VignetteBuilder": "<PERSON><PERSON>r<PERSON>, knitr", "Description": "Set of functions for sparse matrix algebra. Differences with other sparse matrix packages are: (1) we only support (essentially) one sparse matrix format, (2) based on transparent and simple structure(s), (3) tailored for MCMC calculations within G(M)RF. (4) and it is fast and scalable (with the extension package spam64). Documentation about 'spam' is provided by vignettes included in this package, see also <PERSON><PERSON> and <PERSON> (2010) <doi:10.18637/jss.v036.i10>; see 'citation(\"spam\")' for details.", "LazyData": "true", "License": "LGPL-2 | BSD_3_clause + file LICENSE", "URL": "https://www.math.uzh.ch/pages/spam/", "BugReports": "https://git.math.uzh.ch/reinhard.furrer/spam/-/issues", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-6319-2332>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-8545-5263>), <PERSON> [aut] (<https://orcid.org/0000-0002-0349-8698>), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (SPARSEKIT http://www-users.cs.umn.edu/~saad/software/SPARSKIT/), <PERSON><PERSON><PERSON> [ctb] (Fortran Cholesky routines), <PERSON> [ctb] (Fortran Cholesky routines), <PERSON> [ctb] (Fortran Cholesky routines), <PERSON> [ctb] (Fortran Cholesky routines), <PERSON><PERSON><PERSON><PERSON> [ctb] (ARPACK), <PERSON><PERSON><PERSON> [ctb] (ARPACK), <PERSON><PERSON><PERSON> [ctb] (ARPACK), <PERSON> [ctb] (ARPACK)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "stringi": {"Package": "stringi", "Version": "1.8.7", "Source": "Repository", "Date": "2025-03-27", "Title": "Fast and Portable Character String Processing Facilities", "Description": "A collection of character string/text/natural language processing tools for pattern searching (e.g., with 'Java'-like regular expressions or the 'Unicode' collation algorithm), random string generation, case mapping, string transliteration, concatenation, sorting, padding, wrapping, Unicode normalisation, date-time formatting and parsing, and many more. They are fast, consistent, convenient, and - thanks to 'ICU' (International Components for Unicode) - portable across all locales and platforms. Documentation about 'stringi' is provided via its website at <https://stringi.gagolewski.com/> and the paper by <PERSON><PERSON><PERSON><PERSON> (2022, <doi:10.18637/jss.v103.i02>).", "URL": "https://stringi.gagolewski.com/, https://github.com/gagolews/stringi, https://icu.unicode.org/", "BugReports": "https://github.com/gagolews/stringi/issues", "SystemRequirements": "ICU4C (>= 61, optional)", "Type": "Package", "Depends": ["R (>= 3.4)"], "Imports": ["tools", "utils", "stats"], "Biarch": "TRUE", "License": "file LICENSE", "Authors@R": "c(person(given = \"<PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0637-6028\")), person(given = \"<PERSON><PERSON>\", family = \"Tartanus\", role = \"ctb\"), person(\"Unicode, Inc. and others\", role=\"ctb\", comment = \"ICU4C source code, Unicode Character Database\") )", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0637-6028>), <PERSON><PERSON> [ctb], Unicode, Inc. and others [ctb] (ICU4C source code, Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "License_is_FOSS": "yes", "Repository": "CRAN"}, "stringr": {"Package": "stringr", "Version": "1.5.2", "Source": "Repository", "Title": "Simple, Consistent Wrappers for Common String Operations", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A consistent, simple and easy to use set of wrappers around the fantastic 'stringi' package. All function and argument names (and positions) are consistent, all functions deal with \"NA\"'s and zero length vectors in the same way, and the output from one function is easy to feed into the input of another.", "License": "MIT + file LICENSE", "URL": "https://stringr.tidyverse.org, https://github.com/tidyverse/stringr", "BugReports": "https://github.com/tidyverse/stringr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli", "glue (>= 1.6.1)", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "rlang (>= 1.0.0)", "stringi (>= 1.5.3)", "vctrs (>= 0.4.0)"], "Suggests": ["covr", "dplyr", "gt", "htmltools", "htmlwidgets", "knitr", "rmarkdown", "testthat (>= 3.0.0)", "tibble"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "testthat": {"Package": "testthat", "Version": "3.2.3", "Source": "Repository", "Title": "Unit Testing for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"R Core team\", role = \"ctb\", comment = \"Implementation of utils::recover()\") )", "Description": "Software testing is important, but, in part because it is frustrating and boring, many of us avoid it. 'testthat' is a testing framework for R that is easy to learn and use, and integrates with your existing 'workflow'.", "License": "MIT + file LICENSE", "URL": "https://testthat.r-lib.org, https://github.com/r-lib/testthat", "BugReports": "https://github.com/r-lib/testthat/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["brio (>= 1.1.3)", "callr (>= 3.7.3)", "cli (>= 3.6.1)", "desc (>= 1.4.2)", "digest (>= 0.6.33)", "evaluate (>= 1.0.1)", "jsonlite (>= 1.8.7)", "lifecycle (>= 1.0.3)", "magrittr (>= 2.0.3)", "methods", "pkgload (>= *******)", "praise (>= 1.0.0)", "processx (>= 3.8.2)", "ps (>= 1.7.5)", "R6 (>= 2.5.1)", "rlang (>= 1.1.1)", "utils", "waldo (>= 0.6.0)", "withr (>= 3.0.2)"], "Suggests": ["covr", "curl (>= 0.9.5)", "diffviewer (>= 0.1.0)", "knitr", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "S7", "shiny", "usethis", "vctrs (>= 0.1.0)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "watcher, parallel*", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd], R Core team [ctb] (Implementation of utils::recover())", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tibble": {"Package": "tibble", "Version": "3.3.0", "Source": "Repository", "Title": "Simple Data Frames", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>in\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"RStudio\", role = c(\"cph\", \"fnd\")))", "Description": "Provides a 'tbl_df' class (the 'tibble') with stricter checking and better formatting than the traditional data frame.", "License": "MIT + file LICENSE", "URL": "https://tibble.tidyverse.org/, https://github.com/tidyverse/tibble", "BugReports": "https://github.com/tidyverse/tibble/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli", "lifecycle (>= 1.0.0)", "magrit<PERSON>", "methods", "pillar (>= 1.8.1)", "pkgconfig", "rlang (>= 1.0.2)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bench", "bit64", "blob", "brio", "callr", "DiagrammeR", "dplyr", "evaluate", "formattable", "ggplot2", "here", "hms", "htmltools", "knitr", "lubridate", "nycflights13", "pkgload", "purrr", "rmarkdown", "stringi", "testthat (>= 3.0.2)", "tidyr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "vignette-formats, as_tibble, add, invariants", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/autostyle/rmd": "false", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tidycensus": {"Package": "tidycensus", "Version": "1.7.3", "Source": "Repository", "Type": "Package", "Title": "Load US Census Boundary and Attribute Data as 'tidyverse' and 'sf'-Ready Data Frames", "Authors@R": "c( person(given = \"<PERSON>\", family = \"<PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\")), person(given = \"<PERSON>\", family = \"<PERSON>\", email = \"<EMAIL>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>wein\", email = \"<EMAIL>\", role = \"ctb\"))", "Date": "2025-07-24", "URL": "https://walker-data.com/tidycensus/", "BugReports": "https://github.com/walkerke/tidycensus/issues", "Description": "An integrated R interface to several United States Census Bureau  APIs (<https://www.census.gov/data/developers/data-sets.html>) and the US Census Bureau's  geographic boundary files. Allows R users to return Census and ACS data as  tidyverse-ready data frames, and optionally returns a list-column with feature geometry for mapping  and spatial analysis.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "LazyData": "true", "Depends": ["R (>= 3.3.0)"], "Imports": ["httr", "sf", "dplyr (>= 1.0.0)", "tigris", "stringr", "jsonlite (>= 1.5.0)", "purrr", "rvest", "tidyr (>= 1.0.0)", "rapp<PERSON>s", "readr", "xml2", "units", "utils", "rlang", "crayon", "tidyselect"], "Suggests": ["ggplot2", "survey", "srvyr", "terra"], "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ucminf": {"Package": "ucminf", "Version": "1.2.2", "Source": "Repository", "Title": "General-Purpose Unconstrained Non-Linear Optimization", "Authors@R": "c( person(\"<PERSON> <PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\",  role = c(\"ctb\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\") )", "Description": "An algorithm for general-purpose unconstrained non-linear optimization. The algorithm is of quasi-Newton type with BFGS updating of the inverse Hessian and soft line search with a trust region type monitoring of the input to the line search algorithm. The interface of 'ucminf' is designed for easy interchange with 'optim'.", "License": "GPL (>= 2)", "URL": "https://github.com/hdakpo/ucminf", "BugReports": "https://github.com/hdakpo/ucminf/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Depends": ["R (>= 3.5.0)"], "Suggests": ["numDeriv"], "NeedsCompilation": "yes", "Author": "<PERSON> <PERSON><PERSON><PERSON> [ctb, cre], <PERSON> [aut], <PERSON><PERSON> [aut]", "Maintainer": "<PERSON> <PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "units": {"Package": "units", "Version": "0.8-7", "Source": "Repository", "Title": "Measurement Units for R Vectors", "Authors@R": "c(person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8049-7069\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6403-5550\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\") )", "Depends": ["R (>= 3.0.2)"], "Imports": ["Rcpp"], "LinkingTo": ["Rcpp (>= 0.12.10)"], "Suggests": ["NISTunits", "measurements", "xml2", "magrit<PERSON>", "pillar (>= 1.3.0)", "dplyr (>= 1.0.0)", "vctrs (>= 0.3.1)", "ggplot2 (> 3.2.1)", "testthat (>= 3.0.0)", "vdiffr", "knitr", "rvest", "rmarkdown"], "VignetteBuilder": "knitr", "Description": "Support for measurement units in R vectors, matrices and arrays: automatic propagation, conversion, derivation and simplification of units; raising errors in case of unit incompatibility. Compatible with the POSIXct, Date and difftime  classes. Uses the UNIDATA udunits library and unit database for  unit compatibility checking and conversion. Documentation about 'units' is provided in the paper by Pebesma, Mailund & Hiebert (2016, <doi:10.32614/RJ-2016-061>), included in this package as a vignette; see 'citation(\"units\")' for details.", "SystemRequirements": "udunits-2", "License": "GPL-2", "URL": "https://r-quantities.github.io/units/, https://github.com/r-quantities/units", "BugReports": "https://github.com/r-quantities/units/issues", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0001-8049-7069>), <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-6403-5550>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "urlchecker": {"Package": "<PERSON>rl<PERSON><PERSON>", "Version": "1.0.1", "Source": "Repository", "Title": "Run CRAN URL Checks from Older R Versions", "Authors@R": "c( person(\"R Core team\", role = \"aut\", comment = \"The code in urltools.R adapted from the tools package\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"RStudio\", role = c(\"cph\", \"fnd\")) )", "Description": "Provide the URL checking tools available in R 4.1+ as a package for earlier versions of R. Also uses concurrent requests so can be much faster than the serial versions.", "License": "GPL-3", "URL": "https://github.com/r-lib/urlchecker", "BugReports": "https://github.com/r-lib/urlchecker/issues", "Depends": ["R (>= 3.3)"], "Imports": ["cli", "curl", "tools", "xml2"], "Suggests": ["covr"], "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "no", "Author": "R Core team [aut] (The code in urltools.R adapted from the tools package), <PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON><PERSON><PERSON> [aut, cre], <PERSON>tu<PERSON> [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "usethis": {"Package": "usethis", "Version": "3.2.1", "Source": "Repository", "Title": "Automate Package and Project Setup", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"<PERSON>\", \"<PERSON>\", , \"mal<PERSON><PERSON><PERSON><PERSON><EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0299-5825\")), person(\"<PERSON>\", \"Teucher\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-7840-692X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Automate package and project setup tasks that are otherwise performed manually. This includes setting up unit testing, test coverage, continuous integration, Git, 'GitHub', licenses, 'Rcpp', 'RStudio' projects, and more.", "License": "MIT + file LICENSE", "URL": "https://usethis.r-lib.org, https://github.com/r-lib/usethis", "BugReports": "https://github.com/r-lib/usethis/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli (>= 3.0.1)", "clipr (>= 0.3.0)", "crayon", "curl (>= 2.7)", "desc (>= 1.4.2)", "fs (>= 1.3.0)", "gert (>= 1.4.1)", "gh (>= 1.2.1)", "glue (>= 1.3.0)", "jsonlite", "lifecycle (>= 1.0.0)", "purrr", "rapp<PERSON>s", "rlang (>= 1.1.0)", "rprojroot (>= 2.1.1)", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stats", "tools", "utils", "whisker", "withr (>= 2.3.0)", "yaml"], "Suggests": ["covr", "knitr", "magick", "pkgload (>= *******)", "quarto (>= 1.5.1)", "rmarkdown", "roxygen2 (>= 7.1.2)", "spelling (>= 1.2)", "testthat (>= 3.1.8)"], "Config/Needs/website": "r-lib/asciicast, tidyverse/tidytemplate, xml2", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Config/testthat/start-first": "github-actions, release", "Config/usethis/last-upkeep": "2025-04-22", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-6983-2759>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0299-5825>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-7840-692X>), Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "utf8": {"Package": "utf8", "Version": "1.2.6", "Source": "Repository", "Title": "Unicode Text Processing", "Authors@R": "c(person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = c(\"aut\", \"cph\")), person(given = \"Kirill\", family = \"M\\u00fcller\", role = \"cre\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"Unicode, Inc.\", role = c(\"cph\", \"dtc\"), comment = \"Unicode Character Database\"))", "Description": "Process and print 'UTF-8' encoded international text (Unicode). Input, validate, normalize, encode, format, and display.", "License": "Apache License (== 2.0) | file LICENSE", "URL": "https://krlmlr.github.io/utf8/, https://github.com/krlmlr/utf8", "BugReports": "https://github.com/krlmlr/utf8/issues", "Depends": ["R (>= 2.10)"], "Suggests": ["cli", "covr", "knitr", "rlang", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr, rmarkdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph], <PERSON><PERSON> [cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), Unicode, Inc. [cph, dtc] (Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "vctrs": {"Package": "vctrs", "Version": "0.6.5", "Source": "Repository", "Title": "Vector Helpers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"data.table team\", role = \"cph\", comment = \"Radix sort based on data.table's forder() and their contribution to R's order()\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Defines new notions of prototype and size that are used to provide tools for consistent and well-founded type-coercion and size-recycling, and are in turn connected to ideas of type- and size-stability useful for analysing function interfaces.", "License": "MIT + file LICENSE", "URL": "https://vctrs.r-lib.org/, https://github.com/r-lib/vctrs", "BugReports": "https://github.com/r-lib/vctrs/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "glue", "lifecycle (>= 1.0.3)", "rlang (>= 1.1.0)"], "Suggests": ["bit64", "covr", "crayon", "dplyr (>= 0.8.5)", "generics", "knitr", "pillar (>= 1.4.4)", "pkgdown (>= 2.0.1)", "rmarkdown", "testthat (>= 3.0.0)", "tibble (>= 3.1.3)", "waldo (>= 0.2.0)", "withr", "xml2", "zeallot"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-GB", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], data.table team [cph] (Radix sort based on data.table's forder() and their contribution to R's order()), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "visioneval": {"Package": "visioneval", "Version": "3.1.1", "Source": "Bioconductor", "Type": "Package", "Title": "Software Framework for VisionEval Model System", "Date": "2024-01-23", "Author": "<PERSON> <<EMAIL>>[\"aut\", \"cre\"]", "Maintainer": "<PERSON> <<EMAIL>>", "Copyright": "AASHTO, ODOT, <PERSON>", "URL": "https://github.com/gregorbj/VisionEval", "Description": "This package defines all of the functions used to implement the VisionEval model system which defines a system and establishes a software framework for developing and implementing models that support the development of disaggregate strategic planning models for transportation, land use, and related topics. Models are built out of loosely-coupled modules (submodels) that are contained within R packages. A module incorporates model parameters, input and output data specifications, and functions that carry out model calculations. Modules interact by passing data to and from a common datastore. A user builds a model from modules by writing a simple R script which initializes the model and calls the modules in the desired order. This package provides the software framework for initializing and managing the model run environment, managing the common datastore, and running modules.", "License": "file LICENSE", "Depends": ["R (>= 4.0.0)"], "Imports": ["jsonlite", "yaml", "stringr", "reshape2", "sf", "rhdf5", "knitr", "futile.logger"], "Suggests": ["pkgload"], "Encoding": "UTF-8", "Roxygen": "list(load='source')", "RoxygenNote": "7.2.3", "biocViews": "Infrastructure", "NeedsCompilation": "no"}, "vroom": {"Package": "vroom", "Version": "1.6.6", "Source": "Repository", "Title": "Read and Write Rectangular Text Data Quickly", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"<PERSON>\", \"Bearrows\", role = \"ctb\"), person(\"https://github.com/mandreyel/\", role = \"cph\", comment = \"mio library\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"grisu3 implementation\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"grisu3 implementation\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The goal of 'vroom' is to read and write data (like 'csv', 'tsv' and 'fwf') quickly. When reading it uses a quick initial indexing step, then reads the values lazily , so only the data you actually use needs to be read.  The writer formats the data in parallel and writes to disk asynchronously from formatting.", "License": "MIT + file LICENSE", "URL": "https://vroom.r-lib.org, https://github.com/tidyverse/vroom", "BugReports": "https://github.com/tidyverse/vroom/issues", "Depends": ["R (>= 3.6)"], "Imports": ["bit64", "cli (>= 3.2.0)", "crayon", "glue", "hms", "lifecycle (>= 1.0.3)", "methods", "rlang (>= 0.4.2)", "stats", "tibble (>= 2.0.0)", "tidyselect", "tzdb (>= 0.1.1)", "vctrs (>= 0.2.0)", "withr"], "Suggests": ["archive", "bench (>= 1.1.0)", "covr", "curl", "dplyr", "forcats", "fs", "ggplot2", "knitr", "patchwork", "prettyunits", "purrr", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scales", "spelling", "testthat (>= 2.1.0)", "tidyr", "utils", "waldo", "xml2"], "LinkingTo": ["cpp11 (>= 0.2.0)", "progress (>= 1.2.3)", "tzdb (>= 0.1.1)"], "VignetteBuilder": "knitr", "Config/Needs/website": "nycflights13, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "false", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-6983-2759>), <PERSON> [ctb], https://github.com/mandreyel/ [cph] (mio library), <PERSON><PERSON> [cph] (grisu3 implementation), <PERSON><PERSON><PERSON> [cph] (grisu3 implementation), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "waldo": {"Package": "waldo", "Version": "0.6.2", "Source": "Repository", "Title": "Find Differences Between R Objects", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Compare complex R objects and reveal the key differences. Designed particularly for use in testing packages where being able to quickly isolate key differences makes understanding test failures much easier.", "License": "MIT + file LICENSE", "URL": "https://waldo.r-lib.org, https://github.com/r-lib/waldo", "BugReports": "https://github.com/r-lib/waldo/issues", "Depends": ["R (>= 4.0)"], "Imports": ["cli", "diffobj (>= 0.3.4)", "glue", "methods", "rlang (>= 1.1.0)"], "Suggests": ["bit64", "R6", "S7", "testthat (>= 3.0.0)", "withr", "xml2"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "withr": {"Package": "withr", "Version": "3.0.2", "Source": "Repository", "Title": "Run Code 'With' Temporarily Modified Global State", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"kev<PERSON><PERSON>@gmail.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A set of functions to run code 'with' safely and temporarily modified global state. Many of these functions were originally a part of the 'devtools' package, this provides a simple package with limited dependencies to provide access to these functions.", "License": "MIT + file LICENSE", "URL": "https://withr.r-lib.org, https://github.com/r-lib/withr#readme", "BugReports": "https://github.com/r-lib/withr/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "grDevices"], "Suggests": ["callr", "DBI", "knitr", "methods", "rlang", "rmarkdown (>= 2.12)", "RSQLite", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'aaa.R' 'collate.R' 'connection.R' 'db.R' 'defer-exit.R' 'standalone-defer.R' 'defer.R' 'devices.R' 'local_.R' 'with_.R' 'dir.R' 'env.R' 'file.R' 'language.R' 'libpaths.R' 'locale.R' 'makevars.R' 'namespace.R' 'options.R' 'par.R' 'path.R' 'rng.R' 'seed.R' 'wrap.R' 'sink.R' 'tempfile.R' 'timezone.R' 'torture.R' 'utils.R' 'with.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "wk": {"Package": "wk", "Version": "0.9.4", "Source": "Repository", "Title": "Lightweight Well-Known Geometry Parsing", "Authors@R": "c( person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-9415-4582\")), person(given = \"Edzer\", family = \"Peb<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8049-7069\")), person(given = \"<PERSON>\", family = \"North\", email = \"<EMAIL>\", role = c(\"ctb\")) )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Provides a minimal R and C++ API for parsing well-known binary and well-known text representation of geometries to and from R-native formats. Well-known binary is compact and fast to parse; well-known text is human-readable and is useful for writing tests. These formats are useful in R only if the information they contain can be accessed in R, for which high-performance functions are provided here.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "Suggests": ["testthat (>= 3.0.0)", "vctrs (>= 0.3.0)", "sf", "tibble", "readr"], "URL": "https://paleolimbot.github.io/wk/, https://github.com/paleolimbot/wk", "BugReports": "https://github.com/paleolimbot/wk/issues", "Config/testthat/edition": "3", "Depends": ["R (>= 2.10)"], "LazyData": "true", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-9415-4582>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-8049-7069>), <PERSON> [ctb]", "Repository": "CRAN"}, "xfun": {"Package": "xfun", "Version": "0.53", "Source": "Repository", "Type": "Package", "Title": "Supporting Functions for Packages Maintained by '<PERSON><PERSON>'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Dai<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Miscellaneous functions commonly used in other packages maintained by '<PERSON><PERSON>'.", "Depends": ["R (>= 3.2.0)"], "Imports": ["grDevices", "stats", "tools"], "Suggests": ["testit", "parallel", "codetools", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytex (>= 0.30)", "mime", "litedown (>= 0.6)", "commonmark", "knitr (>= 1.50)", "remotes", "pak", "curl", "xml2", "jsonlite", "magick", "yaml", "data.table", "qs"], "License": "MIT + file LICENSE", "URL": "https://github.com/yihui/xfun", "BugReports": "https://github.com/yihui/xfun/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "litedown", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (ORCID: <https://orcid.org/0000-0003-0645-5666>, URL: https://yihui.org), <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-5329-5987>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xml2": {"Package": "xml2", "Version": "1.3.8", "Source": "Repository", "Title": "Parse XML", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"R Foundation\", role = \"ctb\", comment = \"Copy of R-project homepage cached as example\") )", "Description": "Bindings to 'libxml2' for working with XML data using a simple,  consistent interface based on 'XPath' expressions. Also supports XML schema validation; for 'XSLT' transformations see the 'xslt' package.", "License": "MIT + file LICENSE", "URL": "https://xml2.r-lib.org, https://r-lib.r-universe.dev/xml2", "BugReports": "https://github.com/r-lib/xml2/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["cli", "methods", "rlang (>= 1.1.0)"], "Suggests": ["covr", "curl", "httr", "knitr", "magrit<PERSON>", "mockery", "rmarkdown", "testthat (>= 3.2.0)", "xslt"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "SystemRequirements": "libxml2: libxml2-dev (deb), libxml2-devel (rpm)", "Collate": "'S4.R' 'as_list.R' 'xml_parse.R' 'as_xml_document.R' 'classes.R' 'format.R' 'import-standalone-obj-type.R' 'import-standalone-purrr.R' 'import-standalone-types-check.R' 'init.R' 'nodeset_apply.R' 'paths.R' 'utils.R' 'xml2-package.R' 'xml_attr.R' 'xml_children.R' 'xml_document.R' 'xml_find.R' 'xml_missing.R' 'xml_modify.R' 'xml_name.R' 'xml_namespaces.R' 'xml_node.R' 'xml_nodeset.R' 'xml_path.R' 'xml_schema.R' 'xml_serialize.R' 'xml_structure.R' 'xml_text.R' 'xml_type.R' 'xml_url.R' 'xml_write.R' 'zzz.R'", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd], R Foundation [ctb] (Copy of R-project homepage cached as example)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xopen": {"Package": "xopen", "Version": "1.0.1", "Source": "Repository", "Title": "Open System Files, 'URLs', Anything", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Cross platform solution to open files, directories or 'URLs' with their associated programs.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/xopen#readme, https://r-lib.github.io/xopen/", "BugReports": "https://github.com/r-lib/xopen/issues", "Depends": ["R (>= 3.1)"], "Imports": ["processx"], "Suggests": ["ps", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "yaml": {"Package": "yaml", "Version": "2.3.10", "Source": "Repository", "Type": "Package", "Title": "Methods to Convert R Data to YAML and Back", "Date": "2024-07-22", "Suggests": ["RUnit"], "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "License": "BSD_3_clause + file LICENSE", "Description": "Implements the 'libyaml' 'YAML' 1.1 parser and emitter (<https://pyyaml.org/wiki/LibYAML>) for R.", "URL": "https://github.com/vubiostat/r-yaml/", "BugReports": "https://github.com/vubiostat/r-yaml/issues", "NeedsCompilation": "yes", "Repository": "CRAN"}}}