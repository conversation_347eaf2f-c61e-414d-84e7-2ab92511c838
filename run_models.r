library(visioneval)
library(VEModel)
#source("sources/framework/VEModel/R/models.R")

skat_ref_model <- installModel("VERSPM", "skats-base", confirm=FALSE)
skat_ref_model$run()

skat_uc21_model <- installModel("VERSPM", "skats-uc21", confirm = FALSE)
skat_uc21_model$run()

skat_uc22_model <- installModel("VERSPM", "skats-uc22", confirm = FALSE)
skat_usecase2_model$run()

vestate_model <- installModel(
     "VE-State",
     # "VEStatePilot",
     "pilot-base",
     confirm = FALSE
)
# vestate_model$run()

vestate_uc3_model <- installModel(
    "VE-State",
    "pilot-uc3",
    confirm = FALSE
)
vestate_uc3_model$run()
