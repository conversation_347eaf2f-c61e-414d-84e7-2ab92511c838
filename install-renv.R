#!/usr/bin/env Rscript

# VisionEval Linux Installation Bootstrap (renv-based)
#
# This script prepares a project-local renv library on Linux, discovers
# package dependencies across the VisionEval source tree, installs them
# into the renv library, and snapshots the resulting lockfile so future
# sessions can run `renv::restore()`.
#
# Usage:
#   Rscript install-renv.R [path/to/VisionEval-4] [--force] [--ve-pkgs=pkg1,pkg2,...]
#
# Arguments:
#   path/to/VisionEval-4  Optional path to VisionEval project (defaults to script directory)
#   --force               Force reinstallation of packages even if already installed
#   --ve-pkgs=...         Comma-separated list of package paths to install
#
# The optional path argument defaults to the directory that contains
# this script (or the current working directory when run from an
# interactive session).

# Parse command line arguments
args <- commandArgs(trailingOnly = TRUE)
script.path <- tryCatch({
  cmd <- commandArgs(trailingOnly = FALSE)
  script <- cmd[grepl("^--file=", cmd)]
  if (length(script)) normalizePath(sub("^--file=", "", script)) else NA_character_
}, error = function(e) NA_character_)

# Parse flags and options
force_flag <- "--force" %in% args
ve_pkgs_arg <- args[grepl("^--ve-pkgs=", args)]
positional_args <- args[!grepl("^--", args)]

# Determine project path
project <- if (length(positional_args) >= 1) {
  normalizePath(positional_args[[1]], winslash = "/", mustWork = TRUE)
} else if (!is.na(script.path)) {
  normalizePath(dirname(script.path), winslash = "/", mustWork = TRUE)
} else {
  normalizePath(getwd(), winslash = "/", mustWork = TRUE)
}

cran.mirror <- Sys.getenv("VE_CRAN_MIRROR", unset = "https://cloud.r-project.org")
options(repos = c(CRAN = cran.mirror))

if (!requireNamespace("renv", quietly = TRUE)) {
  message("Installing renv from CRAN at ", cran.mirror)
  install.packages("renv")
}

activate.file <- file.path(project, "renv", "activate.R")
lockfile <- file.path(project, "renv.lock")

if (!file.exists(activate.file)) {
  message("Initialising renv project at ", project)
  renv::init(project = project, bare = TRUE, force = TRUE)
} else {
  renv::activate(project = project)
}

for (pkg in c("devtools", "here", "callr", "desc", "usethis")) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    message("Installing ", pkg, " from CRAN at ", cran.mirror)
    install.packages(pkg)
  }
}

# Set default values
default_ve_pkgs <- c("sources/framework/visioneval",
    "sources/framework/VEModel",
    "sources/modules/VE2001NHTS",
    "sources/modules/VETransportSupplyUse",
    "sources/modules/VEHouseholdTravel",
    #"sources/modules/VEScenario",
    #"sources/modules/VETravelDemandMM",
    "sources/modules/VEPowertrainsAndFuels",
    "sources/modules/VESimHouseholds",
    "sources/modules/VELandUse",
    "sources/modules/VESyntheticFirms",
    "sources/modules/VETravelPerformance",
    "sources/modules/VEHouseholdVehicles",
    "sources/modules/VESimLandUseData",
    "sources/modules/VESimLandUse",
    "sources/modules/VETransportSupply",
    "sources/modules/VESimTransportSupply",
    #"sources/modules/VEReports"
    "sources/modules/VESnapshot",
    "/workspace/limwang/VisionEval-Dev/pilot/VESKATSPilot"
    )

# Use command line arguments or defaults
force <- force_flag
ve_pkgs <- if (length(ve_pkgs_arg) > 0) {
  # Extract the value after --ve-pkgs= and split by comma
  pkg_string <- sub("^--ve-pkgs=", "", ve_pkgs_arg[1])
  trimws(strsplit(pkg_string, ",")[[1]])
} else {
  default_ve_pkgs
}

cwd <- getwd()
for (pkg_dir in ve_pkgs) {
  pkg_name <- basename(pkg_dir)

  if (requireNamespace(pkg_name, quietly = TRUE) && !force) {
    message("Skipping ", pkg_dir, " as it is already installed")
    next()
  }
  message("\nInstalling package from directory:", pkg_dir, "\n")
  
  setwd(pkg_dir)
  devtools::document()
  devtools::install(
              ".",
              dependencies = TRUE,
              build = TRUE,
              upgrade = "never"
          )

  message("Successfully installed package from:", pkg_dir, "\n")
  setwd(cwd)
}
